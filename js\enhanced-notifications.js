// Enhanced Notification System
class EnhancedNotificationManager {
    constructor() {
        this.activeNotifications = new Map();
        this.notificationQueue = [];
        this.isProcessingQueue = false;
        this.audioContext = null;
        this.soundEnabled = true;
        this.maxConcurrentNotifications = 3;
        
        this.init();
    }

    async init() {
        this.setupAudioContext();
        this.createNotificationContainer();
        this.loadSettings();
        this.setupEventListeners();
    }

    setupAudioContext() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        } catch (error) {
            console.warn('Audio context not supported:', error);
        }
    }

    createNotificationContainer() {
        if (document.getElementById('enhanced-notifications-container')) return;

        const container = document.createElement('div');
        container.id = 'enhanced-notifications-container';
        container.className = 'enhanced-notifications-container';
        document.body.appendChild(container);

        // Add styles
        this.addNotificationStyles();
    }

    addNotificationStyles() {
        if (document.getElementById('enhanced-notification-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'enhanced-notification-styles';
        styles.textContent = `
            .enhanced-notifications-container {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                pointer-events: none;
                max-width: 400px;
                width: 100%;
            }

            .enhanced-notification {
                background: white;
                border-radius: 16px;
                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
                margin-bottom: 1rem;
                overflow: hidden;
                pointer-events: auto;
                animation: slideInRight 0.5s ease;
                position: relative;
                border-right: 6px solid #e5e7eb;
            }

            .enhanced-notification.critical {
                border-right-color: #dc2626;
                animation: slideInRight 0.5s ease, criticalPulse 2s infinite;
            }

            .enhanced-notification.high {
                border-right-color: #f59e0b;
                animation: slideInRight 0.5s ease, highPulse 3s infinite;
            }

            .enhanced-notification.medium {
                border-right-color: #3b82f6;
            }

            .notification-header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 1rem;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }

            .notification-header.critical {
                background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            }

            .notification-header.high {
                background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            }

            .notification-title {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                font-weight: 600;
                font-size: 1rem;
            }

            .notification-icon {
                font-size: 1.5rem;
                animation: bounce 1s infinite;
            }

            .notification-close {
                background: rgba(255, 255, 255, 0.2);
                border: none;
                color: white;
                border-radius: 50%;
                width: 24px;
                height: 24px;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 0.875rem;
                transition: background 0.2s;
            }

            .notification-close:hover {
                background: rgba(255, 255, 255, 0.3);
            }

            .notification-body {
                padding: 1.5rem;
            }

            .notification-message {
                font-size: 0.875rem;
                line-height: 1.5;
                margin-bottom: 1rem;
                color: #374151;
            }

            .notification-sender {
                background: #f8fafc;
                border-radius: 8px;
                padding: 1rem;
                margin-bottom: 1rem;
                border: 1px solid #e2e8f0;
            }

            .sender-info {
                display: flex;
                align-items: center;
                gap: 0.75rem;
                margin-bottom: 0.5rem;
            }

            .sender-avatar {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-weight: 600;
                font-size: 1.125rem;
            }

            .sender-details h4 {
                margin: 0;
                font-size: 0.875rem;
                font-weight: 600;
                color: #1f2937;
            }

            .sender-details p {
                margin: 0;
                font-size: 0.75rem;
                color: #6b7280;
            }

            .notification-location {
                background: #fef7f0;
                border: 1px solid #fed7aa;
                border-radius: 8px;
                padding: 0.75rem;
                margin-bottom: 1rem;
                font-size: 0.875rem;
            }

            .location-text {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                color: #ea580c;
                font-weight: 500;
            }

            .notification-actions {
                display: flex;
                gap: 0.75rem;
                flex-wrap: wrap;
            }

            .notification-btn {
                flex: 1;
                padding: 0.75rem 1rem;
                border: none;
                border-radius: 8px;
                font-size: 0.875rem;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 0.5rem;
            }

            .notification-btn.primary {
                background: #dc2626;
                color: white;
            }

            .notification-btn.primary:hover {
                background: #b91c1c;
                transform: translateY(-1px);
            }

            .notification-btn.secondary {
                background: #f3f4f6;
                color: #374151;
                border: 1px solid #d1d5db;
            }

            .notification-btn.secondary:hover {
                background: #e5e7eb;
            }

            .notification-timestamp {
                text-align: center;
                font-size: 0.75rem;
                color: #9ca3af;
                margin-top: 1rem;
                padding-top: 1rem;
                border-top: 1px solid #e5e7eb;
            }

            .notification-progress {
                position: absolute;
                bottom: 0;
                left: 0;
                height: 3px;
                background: rgba(59, 130, 246, 0.3);
                transition: width 0.1s linear;
            }

            .notification-progress.critical {
                background: rgba(220, 38, 38, 0.3);
            }

            .notification-progress.high {
                background: rgba(245, 158, 11, 0.3);
            }

            @keyframes slideInRight {
                from {
                    opacity: 0;
                    transform: translateX(100%);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }

            @keyframes slideOutRight {
                from {
                    opacity: 1;
                    transform: translateX(0);
                }
                to {
                    opacity: 0;
                    transform: translateX(100%);
                }
            }

            @keyframes criticalPulse {
                0%, 100% { box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3); }
                50% { box-shadow: 0 20px 60px rgba(220, 38, 38, 0.5); }
            }

            @keyframes highPulse {
                0%, 100% { box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3); }
                50% { box-shadow: 0 20px 60px rgba(245, 158, 11, 0.4); }
            }

            @keyframes bounce {
                0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
                40% { transform: translateY(-5px); }
                60% { transform: translateY(-3px); }
            }

            /* Mobile responsive */
            @media (max-width: 768px) {
                .enhanced-notifications-container {
                    right: 10px;
                    left: 10px;
                    max-width: none;
                }

                .notification-actions {
                    flex-direction: column;
                }

                .notification-btn {
                    flex: none;
                }
            }
        `;
        document.head.appendChild(styles);
    }

    async showEmergencyNotification(alertData) {
        const notification = {
            id: 'notif_' + Date.now(),
            type: 'emergency',
            priority: alertData.priority || 'high',
            alertData: alertData,
            timestamp: new Date(),
            autoClose: false // Emergency notifications don't auto-close
        };

        await this.displayNotification(notification);
        await this.playAlertSound(notification.priority);
        this.vibrateDevice(notification.priority);
    }

    async displayNotification(notification) {
        // Check if we have too many notifications
        if (this.activeNotifications.size >= this.maxConcurrentNotifications) {
            this.notificationQueue.push(notification);
            return;
        }

        const container = document.getElementById('enhanced-notifications-container');
        const notificationElement = this.createNotificationElement(notification);
        
        container.appendChild(notificationElement);
        this.activeNotifications.set(notification.id, {
            notification,
            element: notificationElement,
            startTime: Date.now()
        });

        // Setup auto-close if enabled
        if (notification.autoClose) {
            setTimeout(() => {
                this.closeNotification(notification.id);
            }, notification.duration || 10000);
        }

        // Setup progress bar for auto-close notifications
        if (notification.autoClose) {
            this.startProgressBar(notification.id, notification.duration || 10000);
        }
    }

    createNotificationElement(notification) {
        const element = document.createElement('div');
        element.className = `enhanced-notification ${notification.priority}`;
        element.id = `notification-${notification.id}`;

        const alertData = notification.alertData;
        const senderInfo = alertData.senderInfo || {};
        
        // Get alert type info
        const alertTypes = {
            'ambulance': { icon: '🆘', title: 'طلب إسعاف عاجل' },
            'fire': { icon: '🔥', title: 'إنذار حريق' },
            'civil-defense': { icon: '🛡️', title: 'طلب دفاع مدني' },
            'settler-attack': { icon: '⚠️', title: 'إنذار هجوم مستوطنين' },
            'settlers-present': { icon: '🚷', title: 'تنبيه وجود مستوطنين' },
            'intrusion': { icon: '🚨', title: 'إنذار اقتحام' }
        };

        const alertInfo = alertTypes[alertData.type] || { icon: '📢', title: 'إنذار طوارئ' };
        const senderInitials = this.getInitials(senderInfo.fullName || 'غير معروف');

        element.innerHTML = `
            <div class="notification-header ${notification.priority}">
                <div class="notification-title">
                    <span class="notification-icon">${alertInfo.icon}</span>
                    <span>${alertInfo.title}</span>
                </div>
                <button class="notification-close" onclick="enhancedNotificationManager.closeNotification('${notification.id}')">
                    ✕
                </button>
            </div>
            
            <div class="notification-body">
                ${alertData.customMessage ? `
                    <div class="notification-message">
                        "${alertData.customMessage}"
                    </div>
                ` : ''}
                
                <div class="notification-sender">
                    <div class="sender-info">
                        <div class="sender-avatar">${senderInitials}</div>
                        <div class="sender-details">
                            <h4>${senderInfo.fullName || 'غير معروف'}</h4>
                            <p>${senderInfo.phoneNumber || 'رقم غير متاح'} • ${this.getRoleText(senderInfo.userRole)}</p>
                        </div>
                    </div>
                    <p style="font-size: 0.75rem; color: #6b7280; margin: 0;">
                        المنطقة: ${senderInfo.locationArea || 'غير محدد'}
                    </p>
                </div>

                ${alertData.location ? `
                    <div class="notification-location">
                        <div class="location-text">
                            <span>📍</span>
                            <span>الموقع: ${alertData.location.latitude.toFixed(4)}, ${alertData.location.longitude.toFixed(4)}</span>
                        </div>
                    </div>
                ` : ''}

                <div class="notification-actions">
                    <button class="notification-btn primary" onclick="enhancedNotificationManager.respondToAlert('${notification.id}', 'respond')">
                        <span>🚀</span>
                        الاستجابة
                    </button>
                    <button class="notification-btn secondary" onclick="enhancedNotificationManager.respondToAlert('${notification.id}', 'acknowledge')">
                        <span>✅</span>
                        تم الاستلام
                    </button>
                    ${alertData.location ? `
                        <button class="notification-btn secondary" onclick="enhancedNotificationManager.openLocation('${alertData.location.latitude}', '${alertData.location.longitude}')">
                            <span>🗺️</span>
                            عرض الموقع
                        </button>
                    ` : ''}
                </div>

                <div class="notification-timestamp">
                    ${new Date(alertData.timestamp).toLocaleString('ar-SA')}
                </div>
            </div>

            ${notification.autoClose ? `<div class="notification-progress ${notification.priority}"></div>` : ''}
        `;

        return element;
    }

    async playAlertSound(priority) {
        if (!this.soundEnabled || !this.audioContext) return;

        try {
            // Create different sounds for different priorities
            const frequencies = {
                'critical': [800, 1000, 800, 1000, 800],
                'high': [600, 800, 600],
                'medium': [500, 700]
            };

            const freq = frequencies[priority] || frequencies['medium'];
            
            for (let i = 0; i < freq.length; i++) {
                await this.playTone(freq[i], 0.3, 0.1);
                if (i < freq.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            }
        } catch (error) {
            console.error('Failed to play alert sound:', error);
        }
    }

    async playTone(frequency, duration, volume = 0.1) {
        if (!this.audioContext) return;

        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime);
        oscillator.type = 'sine';

        gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
        gainNode.gain.linearRampToValueAtTime(volume, this.audioContext.currentTime + 0.01);
        gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + duration);

        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + duration);

        return new Promise(resolve => {
            oscillator.onended = resolve;
        });
    }

    vibrateDevice(priority) {
        if (!('vibrate' in navigator)) return;

        const patterns = {
            'critical': [200, 100, 200, 100, 200, 100, 200],
            'high': [300, 100, 300, 100, 300],
            'medium': [200, 100, 200]
        };

        navigator.vibrate(patterns[priority] || patterns['medium']);
    }

    startProgressBar(notificationId, duration) {
        const notification = this.activeNotifications.get(notificationId);
        if (!notification) return;

        const progressBar = notification.element.querySelector('.notification-progress');
        if (!progressBar) return;

        const startTime = Date.now();
        const interval = setInterval(() => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min((elapsed / duration) * 100, 100);
            
            progressBar.style.width = `${100 - progress}%`;
            
            if (progress >= 100) {
                clearInterval(interval);
            }
        }, 100);

        // Store interval for cleanup
        notification.progressInterval = interval;
    }

    closeNotification(notificationId) {
        const notification = this.activeNotifications.get(notificationId);
        if (!notification) return;

        // Clear progress interval if exists
        if (notification.progressInterval) {
            clearInterval(notification.progressInterval);
        }

        // Animate out
        notification.element.style.animation = 'slideOutRight 0.3s ease forwards';
        
        setTimeout(() => {
            if (notification.element.parentNode) {
                notification.element.parentNode.removeChild(notification.element);
            }
            this.activeNotifications.delete(notificationId);
            
            // Process queue
            this.processNotificationQueue();
        }, 300);
    }

    processNotificationQueue() {
        if (this.notificationQueue.length > 0 && this.activeNotifications.size < this.maxConcurrentNotifications) {
            const nextNotification = this.notificationQueue.shift();
            this.displayNotification(nextNotification);
        }
    }

    respondToAlert(notificationId, action) {
        const notification = this.activeNotifications.get(notificationId);
        if (!notification) return;

        const alertData = notification.notification.alertData;
        
        switch (action) {
            case 'respond':
                this.handleAlertResponse(alertData);
                break;
            case 'acknowledge':
                this.handleAlertAcknowledgment(alertData);
                break;
        }

        this.closeNotification(notificationId);
    }

    handleAlertResponse(alertData) {
        // In a real app, this would notify emergency services
        console.log('Responding to alert:', alertData.id);
        
        if (window.notificationManager) {
            window.notificationManager.showBrowserAlert(
                'تم تسجيل الاستجابة',
                'تم إرسال تأكيد الاستجابة للمرسل',
                'success'
            );
        }
    }

    handleAlertAcknowledgment(alertData) {
        console.log('Acknowledging alert:', alertData.id);
        
        if (window.notificationManager) {
            window.notificationManager.showBrowserAlert(
                'تم الاستلام',
                'تم تأكيد استلام الإنذار',
                'info'
            );
        }
    }

    openLocation(latitude, longitude) {
        const url = `https://maps.google.com/maps?q=${latitude},${longitude}`;
        window.open(url, '_blank');
    }

    getInitials(name) {
        return name.split(' ')
            .map(word => word.charAt(0))
            .join('')
            .toUpperCase()
            .substring(0, 2);
    }

    getRoleText(role) {
        const roles = {
            'citizen': 'مواطن',
            'emergency': 'طوارئ',
            'security': 'أمن',
            'medical': 'طبي',
            'fire': 'إطفاء',
            'admin': 'مسؤول'
        };
        return roles[role] || 'مواطن';
    }

    setupEventListeners() {
        // Listen for incoming alerts
        if (window.webSocketManager) {
            window.webSocketManager.subscribe('alert', (alertData) => {
                this.showEmergencyNotification(alertData);
            });
        }
    }

    loadSettings() {
        const settings = JSON.parse(localStorage.getItem('enhancedNotificationSettings') || '{}');
        this.soundEnabled = settings.soundEnabled !== false;
        this.maxConcurrentNotifications = settings.maxConcurrentNotifications || 3;
    }

    saveSettings() {
        const settings = {
            soundEnabled: this.soundEnabled,
            maxConcurrentNotifications: this.maxConcurrentNotifications
        };
        localStorage.setItem('enhancedNotificationSettings', JSON.stringify(settings));
    }

    toggleSound() {
        this.soundEnabled = !this.soundEnabled;
        this.saveSettings();
        return this.soundEnabled;
    }

    clearAllNotifications() {
        for (const [id] of this.activeNotifications) {
            this.closeNotification(id);
        }
        this.notificationQueue = [];
    }
}

// Create global instance
window.enhancedNotificationManager = new EnhancedNotificationManager();

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Test notification (remove in production)
    if (window.location.search.includes('test=notifications')) {
        setTimeout(() => {
            window.enhancedNotificationManager.showEmergencyNotification({
                id: 'test_alert',
                type: 'ambulance',
                priority: 'critical',
                customMessage: 'هذا إنذار تجريبي لاختبار النظام',
                location: { latitude: 31.7683, longitude: 35.2137 },
                timestamp: new Date().toISOString(),
                senderInfo: {
                    fullName: 'أحمد محمد',
                    phoneNumber: '+970591234567',
                    userRole: 'citizen',
                    locationArea: 'القدس'
                }
            });
        }, 2000);
    }
});
