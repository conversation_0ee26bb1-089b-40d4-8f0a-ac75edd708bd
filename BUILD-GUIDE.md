# 🛠️ دليل بناء تطبيق درع الطوارئ

## تحويل التطبيق إلى ملف exe للويندوز

### 📋 المتطلبات الأساسية

#### 1. Node.js
```bash
# تحميل وتثبيت Node.js من الموقع الرسمي
https://nodejs.org/

# التحقق من التثبيت
node --version
npm --version
```

#### 2. Git (اختياري)
```bash
# للحصول على آخر التحديثات
git clone https://github.com/emergency-shield/app.git
```

---

## 🚀 طرق البناء

### الطريقة الأولى: البناء التلقائي (الأسهل)

#### على Windows:
```bash
# انقر مرتين على الملف أو شغل من Command Prompt
build-app.bat
```

#### على Linux/Mac:
```bash
# اجعل الملف قابل للتنفيذ ثم شغله
chmod +x build-app.sh
./build-app.sh
```

### الطريقة الثانية: البناء اليدوي

#### 1. تثبيت المتطلبات
```bash
# تثبيت Electron
npm install electron --save-dev

# تثبيت Electron Builder
npm install electron-builder --save-dev

# تثبيت المكونات الإضافية
npm install electron-updater electron-log electron-store node-notifier --save
```

#### 2. بناء التطبيق
```bash
# بناء لجميع المنصات
npm run build

# بناء للويندوز فقط
npm run build-win

# بناء للويندوز 64-bit
npm run build-win64

# بناء للويندوز 32-bit
npm run build-win32

# بناء إصدار محمول
npm run pack
```

---

## 📁 هيكل المشروع بعد البناء

```
emergency-shield/
├── dist/                           # مجلد الملفات المبنية
│   ├── درع الطوارئ-2.0.0-x64.exe    # مثبت 64-bit
│   ├── درع الطوارئ-2.0.0-ia32.exe   # مثبت 32-bit
│   ├── درع الطوارئ-2.0.0-portable-x64.exe  # محمول 64-bit
│   └── درع الطوارئ-2.0.0-portable-ia32.exe # محمول 32-bit
├── package.json                    # إعدادات المشروع
├── electron-main.js                # الملف الرئيسي لـ Electron
├── electron-preload.js             # ملف الأمان
├── build-app.bat                   # مشغل البناء للويندوز
├── build-app.sh                    # مشغل البناء للينكس/ماك
└── icons/                          # مجلد الأيقونات
    ├── icon.ico                    # أيقونة الويندوز
    ├── icon.png                    # أيقونة عامة
    └── icon.icns                   # أيقونة الماك
```

---

## 🎨 إعداد الأيقونات

### إنشاء الأيقونات المطلوبة:

#### 1. أيقونة Windows (.ico)
```bash
# الحجم المطلوب: 256x256 بكسل
# الصيغة: .ico
# المسار: icons/icon.ico
```

#### 2. أيقونة عامة (.png)
```bash
# الحجم المطلوب: 512x512 بكسل
# الصيغة: .png
# المسار: icons/icon.png
```

#### 3. أيقونة macOS (.icns)
```bash
# الحجم المطلوب: 512x512 بكسل
# الصيغة: .icns
# المسار: icons/icon.icns
```

### أدوات إنشاء الأيقونات:
- **Windows:** IcoFX, GIMP
- **Online:** favicon.io, convertio.co
- **Mac:** Icon Composer, Iconutil

---

## ⚙️ إعدادات البناء المتقدمة

### تخصيص package.json:

```json
{
  "build": {
    "appId": "com.emergencyshield.app",
    "productName": "درع الطوارئ - Emergency Shield",
    "win": {
      "target": [
        {
          "target": "nsis",
          "arch": ["x64", "ia32"]
        },
        {
          "target": "portable",
          "arch": ["x64", "ia32"]
        }
      ],
      "icon": "icons/icon.ico"
    },
    "nsis": {
      "oneClick": false,
      "allowToChangeInstallationDirectory": true,
      "createDesktopShortcut": true,
      "createStartMenuShortcut": true,
      "shortcutName": "درع الطوارئ"
    }
  }
}
```

---

## 🔧 حل المشاكل الشائعة

### ❌ خطأ: "node-gyp rebuild failed"
```bash
# الحل: تثبيت أدوات البناء
npm install --global windows-build-tools
# أو
npm install --global node-gyp
```

### ❌ خطأ: "Cannot find module 'electron'"
```bash
# الحل: إعادة تثبيت Electron
npm uninstall electron
npm install electron --save-dev
```

### ❌ خطأ: "Application entry file not found"
```bash
# الحل: التأكد من وجود electron-main.js
# أو تحديث package.json:
"main": "electron-main.js"
```

### ❌ خطأ: "Icon file not found"
```bash
# الحل: إضافة الأيقونات المطلوبة
mkdir icons
# ضع ملفات الأيقونات في مجلد icons
```

---

## 📦 أنواع الملفات المنتجة

### 1. مثبت NSIS (.exe)
- **الوصف:** مثبت كامل مع إعداد تلقائي
- **المميزات:** اختصارات سطح المكتب، قائمة ابدأ، إلغاء تثبيت
- **الحجم:** أكبر حجماً
- **الاستخدام:** للتوزيع العام

### 2. إصدار محمول (.exe)
- **الوصف:** تطبيق مستقل لا يحتاج تثبيت
- **المميزات:** تشغيل مباشر، لا يترك أثر في النظام
- **الحجم:** أصغر حجماً
- **الاستخدام:** للاستخدام المؤقت أو على أجهزة متعددة

---

## 🚀 تشغيل التطبيق

### بعد البناء:
1. **للمثبت:** انقر مرتين على ملف .exe واتبع التعليمات
2. **للمحمول:** انقر مرتين على الملف المحمول مباشرة

### الميزات الجديدة في إصدار سطح المكتب:
- ✅ تشغيل في الخلفية (System Tray)
- ✅ إشعارات نظام Windows
- ✅ اختصارات لوحة المفاتيح
- ✅ تحديثات تلقائية
- ✅ حفظ الإعدادات محلياً
- ✅ أداء محسن

---

## 📞 الدعم الفني

### في حالة مواجهة مشاكل:

1. **تحقق من المتطلبات:**
   - Node.js 16+ مثبت
   - npm 8+ متاح
   - مساحة كافية على القرص (500MB+)

2. **نظف الملفات المؤقتة:**
   ```bash
   npm run clean
   rm -rf node_modules
   npm install
   ```

3. **تحقق من الأيقونات:**
   - تأكد من وجود ملفات الأيقونات
   - تحقق من الأحجام والصيغ

4. **راجع السجلات:**
   - تحقق من رسائل الخطأ في Terminal
   - راجع ملف package.json

---

## 🎉 النتيجة النهائية

بعد اكتمال البناء بنجاح، ستحصل على:

✅ **تطبيق Windows كامل** (.exe)
✅ **إصدار محمول** لا يحتاج تثبيت
✅ **واجهة سطح مكتب أصلية**
✅ **إشعارات نظام Windows**
✅ **أيقونة في System Tray**
✅ **اختصارات لوحة المفاتيح**
✅ **تحديثات تلقائية**

**🚀 التطبيق جاهز للتوزيع والاستخدام على أي جهاز Windows!**
