const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
    // App info
    getAppVersion: () => ipcRenderer.invoke('get-app-version'),
    getAppInfo: () => ipcRenderer.invoke('get-app-info'),
    
    // Window controls
    openAdminPanel: () => ipcRenderer.invoke('open-admin-panel'),
    minimizeToTray: () => ipcRenderer.invoke('minimize-to-tray'),
    
    // Notifications
    showNotification: (options) => ipcRenderer.invoke('show-notification', options),
    
    // Event listeners
    onShowNotification: (callback) => {
        ipcRenderer.on('show-notification', callback);
    },
    
    onOpenSettings: (callback) => {
        ipcRenderer.on('open-settings', callback);
    },
    
    // Remove listeners
    removeAllListeners: (channel) => {
        ipcRenderer.removeAllListeners(channel);
    },
    
    // Platform info
    platform: process.platform,
    isElectron: true
});

// Enhanced features for desktop app
contextBridge.exposeInMainWorld('desktopFeatures', {
    // File system access (limited)
    saveFile: async (data, filename) => {
        // This would need additional implementation
        return new Promise((resolve) => {
            const blob = new Blob([data], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.click();
            URL.revokeObjectURL(url);
            resolve(true);
        });
    },
    
    // System notifications
    showSystemNotification: (title, body, icon) => {
        if ('Notification' in window && Notification.permission === 'granted') {
            return new Notification(title, { body, icon });
        }
        return null;
    },
    
    // App state
    isDesktopApp: true,
    canMinimizeToTray: true,
    hasSystemTray: true
});

// Emergency Shield specific APIs
contextBridge.exposeInMainWorld('emergencyShieldDesktop', {
    // Emergency features
    sendUrgentAlert: (alertData) => {
        // Send urgent alert through system
        return ipcRenderer.invoke('send-urgent-alert', alertData);
    },
    
    // System integration
    setSystemTrayAlert: (isActive) => {
        return ipcRenderer.invoke('set-system-tray-alert', isActive);
    },
    
    // Auto-start
    setAutoStart: (enabled) => {
        return ipcRenderer.invoke('set-auto-start', enabled);
    },
    
    // Always on top
    setAlwaysOnTop: (enabled) => {
        return ipcRenderer.invoke('set-always-on-top', enabled);
    }
});

// Console logging for debugging
console.log('Emergency Shield Desktop - Preload script loaded');
console.log('Platform:', process.platform);
console.log('Electron version:', process.versions.electron);
console.log('Node version:', process.versions.node);
