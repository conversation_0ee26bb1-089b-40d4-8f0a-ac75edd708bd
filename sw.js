// Service Worker for Emergency Shield App
const CACHE_NAME = 'emergency-shield-v1.0.0';
const STATIC_CACHE = 'emergency-shield-static-v1';
const DYNAMIC_CACHE = 'emergency-shield-dynamic-v1';

// Files to cache for offline functionality
const STATIC_FILES = [
    '/',
    '/index.html',
    '/styles.css',
    '/js/app.js',
    '/js/location.js',
    '/js/alerts.js',
    '/js/notifications.js',
    '/manifest.json',
    '/admin.html',
    // Add icon files when available
    '/icons/icon-192x192.png',
    '/icons/icon-512x512.png'
];

// Install event - cache static files
self.addEventListener('install', event => {
    console.log('Service Worker installing...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then(cache => {
                console.log('Caching static files...');
                return cache.addAll(STATIC_FILES.filter(url => url !== '/'));
            })
            .then(() => {
                console.log('Static files cached successfully');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('Failed to cache static files:', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('Service Worker activating...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
                            console.log('Deleting old cache:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker activated');
                return self.clients.claim();
            })
    );
});

// Fetch event - serve from cache or network
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);

    // Skip non-GET requests
    if (request.method !== 'GET') {
        return;
    }

    // Skip external requests
    if (url.origin !== location.origin) {
        return;
    }

    // Handle API requests
    if (url.pathname.startsWith('/api/')) {
        event.respondWith(handleApiRequest(request));
        return;
    }

    // Handle static files
    event.respondWith(
        caches.match(request)
            .then(cachedResponse => {
                if (cachedResponse) {
                    return cachedResponse;
                }

                // If not in cache, fetch from network
                return fetch(request)
                    .then(networkResponse => {
                        // Cache successful responses
                        if (networkResponse.status === 200) {
                            const responseClone = networkResponse.clone();
                            caches.open(DYNAMIC_CACHE)
                                .then(cache => {
                                    cache.put(request, responseClone);
                                });
                        }
                        return networkResponse;
                    })
                    .catch(() => {
                        // Return offline page for navigation requests
                        if (request.mode === 'navigate') {
                            return caches.match('/index.html');
                        }
                        
                        // Return empty response for other requests
                        return new Response('', {
                            status: 408,
                            statusText: 'Request Timeout'
                        });
                    });
            })
    );
});

// Handle API requests with offline support
async function handleApiRequest(request) {
    const url = new URL(request.url);
    
    try {
        // Try network first
        const networkResponse = await fetch(request);
        
        // Cache successful GET responses
        if (request.method === 'GET' && networkResponse.status === 200) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.log('Network request failed, trying cache...');
        
        // For GET requests, try to serve from cache
        if (request.method === 'GET') {
            const cachedResponse = await caches.match(request);
            if (cachedResponse) {
                return cachedResponse;
            }
        }
        
        // For POST requests (alerts), store in IndexedDB for later sync
        if (request.method === 'POST' && url.pathname === '/api/alerts') {
            try {
                const alertData = await request.json();
                await storeOfflineAlert(alertData);
                
                return new Response(JSON.stringify({
                    success: true,
                    offline: true,
                    message: 'Alert stored for sync when online'
                }), {
                    status: 200,
                    headers: { 'Content-Type': 'application/json' }
                });
            } catch (storeError) {
                console.error('Failed to store offline alert:', storeError);
            }
        }
        
        // Return error response
        return new Response(JSON.stringify({
            error: 'Network unavailable',
            offline: true
        }), {
            status: 503,
            headers: { 'Content-Type': 'application/json' }
        });
    }
}

// Store alert data for offline sync
async function storeOfflineAlert(alertData) {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open('EmergencyShieldDB', 1);
        
        request.onerror = () => reject(request.error);
        
        request.onsuccess = () => {
            const db = request.result;
            const transaction = db.transaction(['offlineAlerts'], 'readwrite');
            const store = transaction.objectStore('offlineAlerts');
            
            const alertWithId = {
                ...alertData,
                id: Date.now().toString(),
                timestamp: new Date().toISOString(),
                synced: false
            };
            
            store.add(alertWithId);
            
            transaction.oncomplete = () => resolve(alertWithId);
            transaction.onerror = () => reject(transaction.error);
        };
        
        request.onupgradeneeded = () => {
            const db = request.result;
            if (!db.objectStoreNames.contains('offlineAlerts')) {
                const store = db.createObjectStore('offlineAlerts', { keyPath: 'id' });
                store.createIndex('timestamp', 'timestamp');
                store.createIndex('synced', 'synced');
            }
        };
    });
}

// Background sync for offline alerts
self.addEventListener('sync', event => {
    if (event.tag === 'sync-alerts') {
        event.waitUntil(syncOfflineAlerts());
    }
});

// Sync offline alerts when connection is restored
async function syncOfflineAlerts() {
    try {
        const db = await openIndexedDB();
        const transaction = db.transaction(['offlineAlerts'], 'readwrite');
        const store = transaction.objectStore('offlineAlerts');
        const index = store.index('synced');
        
        const unsyncedAlerts = await getAllFromIndex(index, false);
        
        for (const alert of unsyncedAlerts) {
            try {
                const response = await fetch('/api/alerts', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(alert)
                });
                
                if (response.ok) {
                    // Mark as synced
                    alert.synced = true;
                    store.put(alert);
                    console.log('Synced offline alert:', alert.id);
                }
            } catch (error) {
                console.error('Failed to sync alert:', alert.id, error);
            }
        }
    } catch (error) {
        console.error('Sync process failed:', error);
    }
}

// Helper function to open IndexedDB
function openIndexedDB() {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open('EmergencyShieldDB', 1);
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve(request.result);
        
        request.onupgradeneeded = () => {
            const db = request.result;
            if (!db.objectStoreNames.contains('offlineAlerts')) {
                const store = db.createObjectStore('offlineAlerts', { keyPath: 'id' });
                store.createIndex('timestamp', 'timestamp');
                store.createIndex('synced', 'synced');
            }
        };
    });
}

// Helper function to get all records from index
function getAllFromIndex(index, value) {
    return new Promise((resolve, reject) => {
        const request = index.getAll(value);
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve(request.result);
    });
}

// Push notification handling
self.addEventListener('push', event => {
    console.log('Push notification received');
    
    let notificationData = {
        title: 'درع الطوارئ',
        body: 'إشعار جديد',
        icon: '/icons/icon-192x192.png',
        badge: '/icons/badge-72x72.png',
        dir: 'rtl',
        lang: 'ar',
        requireInteraction: true,
        actions: [
            {
                action: 'view',
                title: 'عرض'
            },
            {
                action: 'dismiss',
                title: 'إغلاق'
            }
        ]
    };
    
    if (event.data) {
        try {
            const data = event.data.json();
            notificationData = { ...notificationData, ...data };
        } catch (error) {
            console.error('Failed to parse push data:', error);
        }
    }
    
    event.waitUntil(
        self.registration.showNotification(notificationData.title, notificationData)
    );
});

// Notification click handling
self.addEventListener('notificationclick', event => {
    console.log('Notification clicked:', event.action);
    
    event.notification.close();
    
    if (event.action === 'view') {
        event.waitUntil(
            clients.openWindow('/')
        );
    } else if (event.action === 'dismiss') {
        // Just close the notification
        return;
    } else {
        // Default action - open app
        event.waitUntil(
            clients.matchAll({ type: 'window' })
                .then(clientList => {
                    // If app is already open, focus it
                    for (const client of clientList) {
                        if (client.url === '/' && 'focus' in client) {
                            return client.focus();
                        }
                    }
                    // Otherwise open new window
                    if (clients.openWindow) {
                        return clients.openWindow('/');
                    }
                })
        );
    }
    
    // Send message to client about notification action
    event.waitUntil(
        clients.matchAll().then(clientList => {
            clientList.forEach(client => {
                client.postMessage({
                    type: 'NOTIFICATION_ACTION',
                    action: event.action,
                    notification: event.notification.data
                });
            });
        })
    );
});

// Message handling from main app
self.addEventListener('message', event => {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
});

// Periodic background sync (if supported)
self.addEventListener('periodicsync', event => {
    if (event.tag === 'sync-alerts-periodic') {
        event.waitUntil(syncOfflineAlerts());
    }
});

console.log('Service Worker loaded successfully');
