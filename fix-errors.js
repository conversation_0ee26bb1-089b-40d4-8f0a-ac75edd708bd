// Emergency Shield - Error Fixes and Compatibility
// This file contains fixes for common errors and ensures compatibility

(function() {
    'use strict';

    // Fix 1: Ensure all global objects are available
    window.emergencyShieldReady = false;

    // Fix 2: Add missing utility functions
    if (!window.utils) {
        window.utils = {
            formatDate: function(date) {
                return new Date(date).toLocaleDateString('ar-SA');
            },
            
            formatTime: function(date) {
                return new Date(date).toLocaleTimeString('ar-SA');
            },
            
            generateId: function() {
                return 'id_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            },
            
            validatePhoneNumber: function(phone) {
                const phoneRegex = /^\+970[0-9]{9}$/;
                return phoneRegex.test(phone);
            },
            
            sanitizeInput: function(input) {
                if (typeof input !== 'string') return input;
                return input.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
            }
        };
    }

    // Fix 3: Add missing status text functions
    if (!window.getStatusText) {
        window.getStatusText = function(status) {
            const statusTexts = {
                'active': 'نشط',
                'resolved': 'تم الحل',
                'cancelled': 'ملغي',
                'pending': 'في الانتظار'
            };
            return statusTexts[status] || status;
        };
    }

    if (!window.getRoleText) {
        window.getRoleText = function(role) {
            const roleTexts = {
                'citizen': 'مواطن',
                'emergency': 'طوارئ',
                'security': 'أمن',
                'medical': 'طبي',
                'fire': 'إطفاء',
                'admin': 'مسؤول'
            };
            return roleTexts[role] || role;
        };
    }

    // Fix 4: Add error handling for localStorage
    window.safeLocalStorage = {
        setItem: function(key, value) {
            try {
                localStorage.setItem(key, value);
                return true;
            } catch (error) {
                console.error('LocalStorage error:', error);
                return false;
            }
        },
        
        getItem: function(key) {
            try {
                return localStorage.getItem(key);
            } catch (error) {
                console.error('LocalStorage error:', error);
                return null;
            }
        },
        
        removeItem: function(key) {
            try {
                localStorage.removeItem(key);
                return true;
            } catch (error) {
                console.error('LocalStorage error:', error);
                return false;
            }
        }
    };

    // Fix 5: Add fallback for missing APIs
    if (!navigator.geolocation) {
        navigator.geolocation = {
            getCurrentPosition: function(success, error) {
                if (error) {
                    error({ code: 1, message: 'Geolocation not supported' });
                }
            }
        };
    }

    if (!window.Notification) {
        window.Notification = {
            permission: 'denied',
            requestPermission: function() {
                return Promise.resolve('denied');
            }
        };
    }

    // Fix 6: Add missing alert manager initialization
    function initializeAlertManager() {
        if (!window.alertManager && typeof AlertManager !== 'undefined') {
            window.alertManager = new AlertManager();
            console.log('Alert Manager initialized');
        }
    }

    // Fix 7: Add missing enhanced notification manager initialization
    function initializeEnhancedNotifications() {
        if (!window.enhancedNotificationManager && typeof EnhancedNotificationManager !== 'undefined') {
            window.enhancedNotificationManager = new EnhancedNotificationManager();
            console.log('Enhanced Notification Manager initialized');
        }
    }

    // Fix 8: Add missing SMS service initialization
    function initializeSMSService() {
        if (!window.smsService && typeof SMSService !== 'undefined') {
            window.smsService = new SMSService();
            console.log('SMS Service initialized');
        }
    }

    // Fix 9: Add missing auth manager initialization
    function initializeAuthManager() {
        if (!window.authManager && typeof AuthManager !== 'undefined') {
            window.authManager = new AuthManager();
            console.log('Auth Manager initialized');
        }
    }

    // Fix 10: Add missing admin dashboard initialization
    function initializeAdminDashboard() {
        if (!window.adminDashboard && typeof AdminDashboard !== 'undefined') {
            window.adminDashboard = new AdminDashboard();
            console.log('Admin Dashboard initialized');
        }
    }

    // Fix 11: Add error boundary for uncaught errors
    window.addEventListener('error', function(event) {
        console.error('Uncaught error:', event.error);
        
        // Show user-friendly error message
        if (document.getElementById('error-message')) {
            document.getElementById('error-message').textContent = 
                'حدث خطأ غير متوقع. يرجى إعادة تحميل الصفحة.';
            document.getElementById('error-message').style.display = 'block';
        }
    });

    // Fix 12: Add promise rejection handler
    window.addEventListener('unhandledrejection', function(event) {
        console.error('Unhandled promise rejection:', event.reason);
        event.preventDefault();
    });

    // Fix 13: Initialize all managers when DOM is ready
    function initializeAllManagers() {
        setTimeout(() => {
            initializeAlertManager();
            initializeEnhancedNotifications();
            initializeSMSService();
            initializeAuthManager();
            initializeAdminDashboard();
            
            window.emergencyShieldReady = true;
            console.log('Emergency Shield fully initialized');
            
            // Dispatch ready event
            window.dispatchEvent(new CustomEvent('emergencyShieldReady'));
        }, 100);
    }

    // Fix 14: Add compatibility checks
    function checkCompatibility() {
        const required = [
            'localStorage',
            'JSON',
            'fetch',
            'Promise'
        ];

        const missing = required.filter(feature => !(feature in window));
        
        if (missing.length > 0) {
            console.warn('Missing features:', missing);
            alert('متصفحك لا يدعم بعض الميزات المطلوبة. يرجى تحديث المتصفح.');
        }
    }

    // Fix 15: Add retry mechanism for failed operations
    window.retryOperation = function(operation, maxRetries = 3, delay = 1000) {
        return new Promise((resolve, reject) => {
            let attempts = 0;
            
            function attempt() {
                attempts++;
                operation()
                    .then(resolve)
                    .catch(error => {
                        if (attempts < maxRetries) {
                            setTimeout(attempt, delay);
                        } else {
                            reject(error);
                        }
                    });
            }
            
            attempt();
        });
    };

    // Fix 16: Add network status monitoring
    window.networkStatus = {
        online: navigator.onLine,
        
        init: function() {
            window.addEventListener('online', () => {
                this.online = true;
                console.log('Network: Online');
            });
            
            window.addEventListener('offline', () => {
                this.online = false;
                console.log('Network: Offline');
            });
        }
    };

    // Initialize everything when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            checkCompatibility();
            window.networkStatus.init();
            initializeAllManagers();
        });
    } else {
        checkCompatibility();
        window.networkStatus.init();
        initializeAllManagers();
    }

    console.log('Emergency Shield error fixes loaded');
})();
