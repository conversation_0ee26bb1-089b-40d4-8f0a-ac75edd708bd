// Authentication Manager
class AuthManager {
    constructor() {
        this.currentUser = null;
        this.verificationCode = null;
        this.phoneNumber = null;
        this.countdownTimer = null;
        this.resendTimeout = 60;
        
        this.init();
    }

    async init() {
        // Hide loading screen after 2 seconds
        setTimeout(() => {
            document.getElementById('loading-screen').classList.add('hidden');
            document.getElementById('auth-container').classList.remove('hidden');
        }, 2000);

        // Check if user is already logged in
        const savedUser = this.getSavedUser();
        if (savedUser && this.isValidSession(savedUser)) {
            this.currentUser = savedUser;
            this.redirectToApp();
            return;
        }

        this.setupEventListeners();
        this.setupVerificationInput();
    }

    setupEventListeners() {
        // Welcome screen buttons
        document.getElementById('start-registration').addEventListener('click', () => {
            this.showScreen('phone-screen');
        });

        document.getElementById('start-login').addEventListener('click', () => {
            this.showScreen('login-screen');
        });

        // Phone registration form
        document.getElementById('phone-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handlePhoneRegistration(e);
        });

        // Verification form
        document.getElementById('verification-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleVerification(e);
        });

        // Login form
        document.getElementById('login-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin(e);
        });

        // Resend code button
        document.getElementById('resend-code').addEventListener('click', () => {
            this.resendVerificationCode();
        });

        // Continue to app button
        document.getElementById('continue-to-app').addEventListener('click', () => {
            this.redirectToApp();
        });
    }

    setupVerificationInput() {
        const inputs = document.querySelectorAll('.verification-input input');
        inputs.forEach((input, index) => {
            input.addEventListener('input', (e) => {
                const value = e.target.value;
                if (value && index < inputs.length - 1) {
                    inputs[index + 1].focus();
                }
            });

            input.addEventListener('keydown', (e) => {
                if (e.key === 'Backspace' && !e.target.value && index > 0) {
                    inputs[index - 1].focus();
                }
            });
        });
    }

    showScreen(screenId) {
        // Hide all screens
        document.querySelectorAll('.auth-screen').forEach(screen => {
            screen.classList.remove('active');
        });

        // Show target screen
        document.getElementById(screenId).classList.add('active');
    }

    async handlePhoneRegistration(e) {
        const formData = new FormData(e.target);
        const userData = {
            fullName: formData.get('fullName'),
            countryCode: formData.get('countryCode'),
            phoneNumber: formData.get('phoneNumber'),
            userRole: formData.get('userRole'),
            locationArea: formData.get('locationArea')
        };

        // Validate form
        if (!this.validateRegistrationForm(userData)) {
            return;
        }

        // Show loading state
        const submitBtn = e.target.querySelector('button[type="submit"]');
        this.setButtonLoading(submitBtn, true);

        try {
            // Format phone number
            this.phoneNumber = userData.countryCode + userData.phoneNumber;
            
            // Generate verification code
            this.verificationCode = this.generateVerificationCode();
            
            // Simulate SMS sending
            await this.sendSMS(this.phoneNumber, this.verificationCode);
            
            // Store user data temporarily
            this.tempUserData = {
                ...userData,
                phoneNumber: this.phoneNumber,
                registrationDate: new Date().toISOString(),
                userId: this.generateUserId(),
                ipAddress: await this.getUserIP()
            };

            // Update verification screen
            document.getElementById('sent-phone-number').textContent = this.phoneNumber;
            
            // Show verification screen
            this.showScreen('verification-screen');
            this.startCountdown();

        } catch (error) {
            this.showError('حدث خطأ في إرسال رمز التحقق. يرجى المحاولة مرة أخرى.');
            console.error('Registration error:', error);
        } finally {
            this.setButtonLoading(submitBtn, false);
        }
    }

    async handleVerification(e) {
        const inputs = document.querySelectorAll('.verification-input input');
        const enteredCode = Array.from(inputs).map(input => input.value).join('');

        if (enteredCode.length !== 6) {
            this.showError('يرجى إدخال الرمز كاملاً');
            return;
        }

        const submitBtn = e.target.querySelector('button[type="submit"]');
        this.setButtonLoading(submitBtn, true);

        try {
            // Verify code
            if (enteredCode === this.verificationCode) {
                // Create user account
                this.currentUser = {
                    ...this.tempUserData,
                    isVerified: true,
                    lastLogin: new Date().toISOString(),
                    sessionToken: this.generateSessionToken()
                };

                // Save user data
                this.saveUser(this.currentUser);
                
                // Show success screen
                this.displayUserInfo();
                this.showScreen('success-screen');

                // Log registration
                await this.logActivity('user_registered', {
                    userId: this.currentUser.userId,
                    phoneNumber: this.currentUser.phoneNumber,
                    userRole: this.currentUser.userRole
                });

            } else {
                this.showError('رمز التحقق غير صحيح');
                // Clear inputs
                inputs.forEach(input => input.value = '');
                inputs[0].focus();
            }

        } catch (error) {
            this.showError('حدث خطأ في التحقق. يرجى المحاولة مرة أخرى.');
            console.error('Verification error:', error);
        } finally {
            this.setButtonLoading(submitBtn, false);
        }
    }

    async handleLogin(e) {
        const formData = new FormData(e.target);
        const countryCode = formData.get('countryCode');
        const phoneNumber = formData.get('phoneNumber');
        const fullPhone = countryCode + phoneNumber;

        const submitBtn = e.target.querySelector('button[type="submit"]');
        this.setButtonLoading(submitBtn, true);

        try {
            // Check if user exists
            const existingUser = this.findUserByPhone(fullPhone);
            if (!existingUser) {
                this.showError('رقم الهاتف غير مسجل. يرجى التسجيل أولاً.');
                return;
            }

            // Generate and send verification code
            this.phoneNumber = fullPhone;
            this.verificationCode = this.generateVerificationCode();
            await this.sendSMS(this.phoneNumber, this.verificationCode);

            // Store user data for login
            this.tempUserData = existingUser;

            // Update verification screen
            document.getElementById('sent-phone-number').textContent = this.phoneNumber;
            
            // Show verification screen
            this.showScreen('verification-screen');
            this.startCountdown();

        } catch (error) {
            this.showError('حدث خطأ في تسجيل الدخول. يرجى المحاولة مرة أخرى.');
            console.error('Login error:', error);
        } finally {
            this.setButtonLoading(submitBtn, false);
        }
    }

    validateRegistrationForm(userData) {
        // Validate full name
        if (!userData.fullName || userData.fullName.length < 2) {
            this.showError('يرجى إدخال الاسم الكامل');
            return false;
        }

        // Validate phone number
        if (!userData.phoneNumber || !/^[0-9]{9}$/.test(userData.phoneNumber)) {
            this.showError('يرجى إدخال رقم هاتف صحيح (9 أرقام)');
            return false;
        }

        // Validate user role
        if (!userData.userRole) {
            this.showError('يرجى اختيار نوع الحساب');
            return false;
        }

        // Validate location area
        if (!userData.locationArea || userData.locationArea.length < 2) {
            this.showError('يرجى إدخال المنطقة');
            return false;
        }

        // Check if phone number already exists
        const existingUser = this.findUserByPhone(userData.countryCode + userData.phoneNumber);
        if (existingUser) {
            this.showError('رقم الهاتف مسجل مسبقاً. يرجى تسجيل الدخول.');
            return false;
        }

        return true;
    }

    generateVerificationCode() {
        return Math.floor(100000 + Math.random() * 900000).toString();
    }

    generateUserId() {
        return 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    generateSessionToken() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 16);
    }

    async sendSMS(phoneNumber, code) {
        // Simulate SMS sending - in real app, this would call SMS service
        console.log(`SMS sent to ${phoneNumber}: Your verification code is ${code}`);
        
        // For demo purposes, show the code in console
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            alert(`رمز التحقق للاختبار: ${code}`);
        }

        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        return { success: true, messageId: 'msg_' + Date.now() };
    }

    async resendVerificationCode() {
        try {
            this.verificationCode = this.generateVerificationCode();
            await this.sendSMS(this.phoneNumber, this.verificationCode);
            this.startCountdown();
            this.showSuccess('تم إعادة إرسال الرمز');
        } catch (error) {
            this.showError('فشل في إعادة الإرسال');
        }
    }

    startCountdown() {
        let timeLeft = this.resendTimeout;
        const countdownElement = document.getElementById('countdown');
        const resendButton = document.getElementById('resend-code');
        
        resendButton.disabled = true;
        
        this.countdownTimer = setInterval(() => {
            timeLeft--;
            countdownElement.textContent = timeLeft;
            
            if (timeLeft <= 0) {
                clearInterval(this.countdownTimer);
                resendButton.disabled = false;
                resendButton.innerHTML = 'إعادة الإرسال';
            }
        }, 1000);
    }

    displayUserInfo() {
        const userInfoDisplay = document.getElementById('user-info-display');
        userInfoDisplay.innerHTML = `
            <div class="user-info-item">
                <span class="user-info-label">الاسم:</span>
                <span class="user-info-value">${this.currentUser.fullName}</span>
            </div>
            <div class="user-info-item">
                <span class="user-info-label">رقم الهاتف:</span>
                <span class="user-info-value">${this.currentUser.phoneNumber}</span>
            </div>
            <div class="user-info-item">
                <span class="user-info-label">نوع الحساب:</span>
                <span class="user-info-value">${this.getRoleText(this.currentUser.userRole)}</span>
            </div>
            <div class="user-info-item">
                <span class="user-info-label">المنطقة:</span>
                <span class="user-info-value">${this.currentUser.locationArea}</span>
            </div>
        `;
    }

    getRoleText(role) {
        const roles = {
            'citizen': 'مواطن',
            'emergency': 'طوارئ',
            'security': 'أمن',
            'medical': 'طبي',
            'fire': 'إطفاء',
            'admin': 'مسؤول'
        };
        return roles[role] || role;
    }

    // User management
    saveUser(user) {
        const users = this.getAllUsers();
        users[user.userId] = user;
        localStorage.setItem('emergencyShieldUsers', JSON.stringify(users));
        localStorage.setItem('currentUser', JSON.stringify(user));
    }

    getSavedUser() {
        const saved = localStorage.getItem('currentUser');
        return saved ? JSON.parse(saved) : null;
    }

    getAllUsers() {
        const saved = localStorage.getItem('emergencyShieldUsers');
        return saved ? JSON.parse(saved) : {};
    }

    findUserByPhone(phoneNumber) {
        const users = this.getAllUsers();
        return Object.values(users).find(user => user.phoneNumber === phoneNumber);
    }

    isValidSession(user) {
        if (!user.sessionToken || !user.lastLogin) return false;
        
        const lastLogin = new Date(user.lastLogin);
        const now = new Date();
        const daysDiff = (now - lastLogin) / (1000 * 60 * 60 * 24);
        
        return daysDiff < 30; // Session valid for 30 days
    }

    async getUserIP() {
        try {
            const response = await fetch('https://api.ipify.org?format=json');
            const data = await response.json();
            return data.ip;
        } catch (error) {
            console.error('Failed to get IP:', error);
            return 'unknown';
        }
    }

    async logActivity(action, data) {
        const logEntry = {
            id: Date.now().toString(),
            action,
            data,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            ip: await this.getUserIP()
        };

        const logs = JSON.parse(localStorage.getItem('activityLogs') || '[]');
        logs.unshift(logEntry);
        
        // Keep only last 1000 logs
        if (logs.length > 1000) {
            logs.splice(1000);
        }
        
        localStorage.setItem('activityLogs', JSON.stringify(logs));
    }

    redirectToApp() {
        // Update user's last login
        if (this.currentUser) {
            this.currentUser.lastLogin = new Date().toISOString();
            this.saveUser(this.currentUser);
        }
        
        window.location.href = 'index.html';
    }

    // UI helpers
    setButtonLoading(button, loading) {
        if (loading) {
            button.classList.add('loading');
            button.disabled = true;
        } else {
            button.classList.remove('loading');
            button.disabled = false;
        }
    }

    showError(message) {
        // Create or update error message
        this.showMessage(message, 'error');
    }

    showSuccess(message) {
        this.showMessage(message, 'success');
    }

    showMessage(message, type) {
        // Remove existing messages
        const existingMessage = document.querySelector('.auth-message');
        if (existingMessage) {
            existingMessage.remove();
        }

        // Create new message
        const messageDiv = document.createElement('div');
        messageDiv.className = `auth-message ${type}`;
        messageDiv.textContent = message;
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            left: 20px;
            background: ${type === 'error' ? '#fef2f2' : '#f0fdf4'};
            color: ${type === 'error' ? '#dc2626' : '#16a34a'};
            border: 1px solid ${type === 'error' ? '#fecaca' : '#bbf7d0'};
            border-radius: 12px;
            padding: 1rem;
            text-align: center;
            font-weight: 500;
            z-index: 10000;
            animation: slideDown 0.3s ease;
        `;

        document.body.appendChild(messageDiv);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.remove();
            }
        }, 5000);
    }

    // Get current user info
    getCurrentUser() {
        return this.currentUser;
    }

    logout() {
        localStorage.removeItem('currentUser');
        this.currentUser = null;
        window.location.href = 'auth.html';
    }
}

// Global functions for modal handling
function showTerms() {
    document.getElementById('terms-modal').classList.remove('hidden');
}

function showPrivacy() {
    document.getElementById('privacy-modal').classList.remove('hidden');
}

function hideModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}

// Initialize auth manager
document.addEventListener('DOMContentLoaded', () => {
    window.authManager = new AuthManager();
});

// Add CSS for slide down animation
const style = document.createElement('style');
style.textContent = `
    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
`;
document.head.appendChild(style);
