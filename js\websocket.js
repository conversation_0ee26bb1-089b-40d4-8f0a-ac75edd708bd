// Real-time Communication Manager
class WebSocketManager {
    constructor() {
        this.ws = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.heartbeatInterval = null;
        this.messageQueue = [];
        this.subscribers = new Map();

        this.init();
    }

    async init() {
        // For now, simulate WebSocket with local events
        this.simulateRealTimeUpdates();
        this.setupEventListeners();
    }

    // Simulate real-time updates using local events and intervals
    simulateRealTimeUpdates() {
        // Simulate connection
        setTimeout(() => {
            this.isConnected = true;
            this.notifySubscribers('connected', { status: 'connected' });
            this.updateConnectionStatus(true);
        }, 1000);

        // Simulate periodic updates
        setInterval(() => {
            if (this.isConnected) {
                this.simulateIncomingAlert();
            }
        }, 30000); // Every 30 seconds

        // Simulate heartbeat
        this.heartbeatInterval = setInterval(() => {
            if (this.isConnected) {
                this.sendHeartbeat();
            }
        }, 10000); // Every 10 seconds
    }

    simulateIncomingAlert() {
        // Randomly generate incoming alerts for demonstration
        if (Math.random() < 0.3) { // 30% chance
            const alertTypes = ['ambulance', 'fire', 'civil-defense', 'settler-attack', 'settlers-present', 'intrusion'];
            const priorities = ['critical', 'high', 'medium'];

            const simulatedAlert = {
                id: 'sim_' + Date.now(),
                type: alertTypes[Math.floor(Math.random() * alertTypes.length)],
                priority: priorities[Math.floor(Math.random() * priorities.length)],
                location: {
                    latitude: 31.7683 + (Math.random() - 0.5) * 0.1,
                    longitude: 35.2137 + (Math.random() - 0.5) * 0.1
                },
                timestamp: new Date().toISOString(),
                status: 'active',
                userId: 'user_' + Math.floor(Math.random() * 1000),
                distance: Math.floor(Math.random() * 5000) + 100,
                description: 'إنذار محاكي للاختبار'
            };

            this.handleIncomingAlert(simulatedAlert);
        }
    }

    handleIncomingAlert(alert) {
        // Add to alert manager if available
        if (window.alertManager) {
            window.alertManager.alerts.unshift(alert);
            window.alertManager.saveAlertsToStorage();
        }

        // Send notification
        if (window.notificationManager) {
            window.notificationManager.sendIncomingAlert(alert);
        }

        // Notify subscribers
        this.notifySubscribers('alert', alert);

        // Update UI if admin dashboard is open
        if (window.adminDashboard && window.adminDashboard.isAuthenticated) {
            window.adminDashboard.loadDashboardData();
        }
    }

    // WebSocket connection methods (for future real implementation)
    connect(url = 'ws://localhost:8080/ws') {
        try {
            this.ws = new WebSocket(url);
            this.setupWebSocketHandlers();
        } catch (error) {
            console.error('WebSocket connection failed:', error);
            this.handleConnectionError();
        }
    }

    setupWebSocketHandlers() {
        if (!this.ws) return;

        this.ws.onopen = () => {
            console.log('WebSocket connected');
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.updateConnectionStatus(true);
            this.notifySubscribers('connected', { status: 'connected' });

            // Send queued messages
            this.sendQueuedMessages();

            // Start heartbeat
            this.startHeartbeat();
        };

        this.ws.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleMessage(data);
            } catch (error) {
                console.error('Failed to parse WebSocket message:', error);
            }
        };

        this.ws.onclose = (event) => {
            console.log('WebSocket disconnected:', event.code, event.reason);
            this.isConnected = false;
            this.updateConnectionStatus(false);
            this.notifySubscribers('disconnected', { code: event.code, reason: event.reason });

            // Attempt to reconnect
            if (this.reconnectAttempts < this.maxReconnectAttempts) {
                this.scheduleReconnect();
            }
        };

        this.ws.onerror = (error) => {
            console.error('WebSocket error:', error);
            this.handleConnectionError();
        };
    }

    handleMessage(data) {
        switch (data.type) {
            case 'alert':
                this.handleIncomingAlert(data.payload);
                break;
            case 'alert_update':
                this.handleAlertUpdate(data.payload);
                break;
            case 'user_status':
                this.handleUserStatusUpdate(data.payload);
                break;
            case 'system_message':
                this.handleSystemMessage(data.payload);
                break;
            case 'heartbeat':
                // Heartbeat response
                break;
            default:
                console.log('Unknown message type:', data.type);
        }
    }

    handleAlertUpdate(update) {
        if (window.alertManager) {
            const alert = window.alertManager.activeAlerts.get(update.alertId);
            if (alert) {
                Object.assign(alert, update);
                window.alertManager.saveAlertsToStorage();
            }
        }

        this.notifySubscribers('alert_update', update);
    }

    handleUserStatusUpdate(update) {
        this.notifySubscribers('user_status', update);
    }

    handleSystemMessage(message) {
        if (window.notificationManager) {
            window.notificationManager.showBrowserAlert(
                'رسالة النظام',
                message.content,
                'info'
            );
        }

        this.notifySubscribers('system_message', message);
    }

    // Send methods
    sendMessage(type, payload) {
        const message = {
            type,
            payload,
            timestamp: new Date().toISOString(),
            userId: this.getUserId()
        };

        if (this.isConnected && this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(message));
        } else {
            // Queue message for later
            this.messageQueue.push(message);
        }
    }

    sendAlert(alert) {
        this.sendMessage('alert', alert);
    }

    sendAlertUpdate(alertId, update) {
        this.sendMessage('alert_update', { alertId, ...update });
    }

    sendUserStatus(status) {
        this.sendMessage('user_status', {
            userId: this.getUserId(),
            status,
            location: window.locationManager ? window.locationManager.currentLocation : null
        });
    }

    sendHeartbeat() {
        this.sendMessage('heartbeat', { timestamp: Date.now() });
    }

    sendQueuedMessages() {
        while (this.messageQueue.length > 0) {
            const message = this.messageQueue.shift();
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                this.ws.send(JSON.stringify(message));
            } else {
                // Put it back if connection is lost
                this.messageQueue.unshift(message);
                break;
            }
        }
    }

    // Connection management
    scheduleReconnect() {
        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

        console.log(`Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts})`);

        setTimeout(() => {
            this.connect();
        }, delay);
    }

    handleConnectionError() {
        this.isConnected = false;
        this.updateConnectionStatus(false);
        this.notifySubscribers('error', { message: 'Connection error' });
    }

    startHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
        }

        this.heartbeatInterval = setInterval(() => {
            if (this.isConnected) {
                this.sendHeartbeat();
            }
        }, 30000); // Every 30 seconds
    }

    // Subscription management
    subscribe(event, callback) {
        if (!this.subscribers.has(event)) {
            this.subscribers.set(event, new Set());
        }
        this.subscribers.get(event).add(callback);

        // Return unsubscribe function
        return () => {
            const callbacks = this.subscribers.get(event);
            if (callbacks) {
                callbacks.delete(callback);
            }
        };
    }

    notifySubscribers(event, data) {
        const callbacks = this.subscribers.get(event);
        if (callbacks) {
            callbacks.forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error('Subscriber callback error:', error);
                }
            });
        }
    }

    // Event listeners setup
    setupEventListeners() {
        // Listen for online/offline events
        window.addEventListener('online', () => {
            if (!this.isConnected) {
                this.connect();
            }
        });

        window.addEventListener('offline', () => {
            this.updateConnectionStatus(false);
        });

        // Listen for visibility change
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && !this.isConnected) {
                this.connect();
            }
        });
    }

    // UI updates
    updateConnectionStatus(isOnline) {
        const statusDot = document.getElementById('connection-status');
        const statusText = document.querySelector('.status-text');

        if (statusDot && statusText) {
            if (isOnline && this.isConnected) {
                statusDot.className = 'status-dot online';
                statusText.textContent = 'متصل';
            } else {
                statusDot.className = 'status-dot offline';
                statusText.textContent = 'غير متصل';
            }
        }
    }

    // Utility methods
    getUserId() {
        let userId = localStorage.getItem('userId');
        if (!userId) {
            userId = 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            localStorage.setItem('userId', userId);
        }
        return userId;
    }

    // Cleanup
    disconnect() {
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }

        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }

        this.isConnected = false;
        this.updateConnectionStatus(false);
    }

    // Status getters
    getConnectionStatus() {
        return {
            isConnected: this.isConnected,
            reconnectAttempts: this.reconnectAttempts,
            queuedMessages: this.messageQueue.length,
            subscribers: Array.from(this.subscribers.keys())
        };
    }
}

// Create global instance
window.webSocketManager = new WebSocketManager();

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Subscribe to alerts for real-time updates
    if (window.webSocketManager) {
        window.webSocketManager.subscribe('alert', (alert) => {
            console.log('New alert received:', alert);

            // Update UI if needed
            if (window.emergencyApp) {
                // Refresh recent alerts or update counters
            }
        });

        window.webSocketManager.subscribe('connected', () => {
            console.log('Real-time connection established');
        });

        window.webSocketManager.subscribe('disconnected', () => {
            console.log('Real-time connection lost');
        });
    }
});