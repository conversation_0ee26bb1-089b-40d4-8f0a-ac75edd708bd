<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سريع - درع الطوارئ</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 2rem;
            color: white;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2rem;
            color: #333;
        }
        
        .test-btn {
            background: #dc2626;
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 12px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            margin: 0.5rem;
            transition: all 0.2s;
            display: block;
            width: 100%;
        }
        
        .test-btn:hover {
            background: #b91c1c;
            transform: translateY(-2px);
        }
        
        .test-result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 8px;
            font-family: monospace;
            font-size: 0.875rem;
        }
        
        .success {
            background: #f0fdf4;
            border: 1px solid #16a34a;
            color: #16a34a;
        }
        
        .error {
            background: #fef2f2;
            border: 1px solid #dc2626;
            color: #dc2626;
        }
        
        .info {
            background: #eff6ff;
            border: 1px solid #3b82f6;
            color: #3b82f6;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🛡️ اختبار سريع - درع الطوارئ</h1>
        <p>اختبار سريع لجميع الوظائف الأساسية</p>
        
        <button class="test-btn" onclick="testBasicFunctions()">
            اختبار الوظائف الأساسية
        </button>
        
        <button class="test-btn" onclick="testAuthentication()">
            اختبار نظام المصادقة
        </button>
        
        <button class="test-btn" onclick="testNotifications()">
            اختبار الإشعارات
        </button>
        
        <button class="test-btn" onclick="testSMS()">
            اختبار خدمة SMS
        </button>
        
        <button class="test-btn" onclick="testDatabase()">
            اختبار قاعدة البيانات
        </button>
        
        <button class="test-btn" onclick="testLocation()">
            اختبار تحديد الموقع
        </button>
        
        <button class="test-btn" onclick="testFullWorkflow()">
            اختبار التدفق الكامل
        </button>
        
        <div id="test-results"></div>
    </div>

    <!-- Load all scripts -->
    <script src="js/database.js"></script>
    <script src="js/location.js"></script>
    <script src="js/alerts.js"></script>
    <script src="js/notifications.js"></script>
    <script src="js/maps.js"></script>
    <script src="js/websocket.js"></script>
    <script src="js/sms-service.js"></script>
    <script src="js/enhanced-notifications.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/admin.js"></script>
    <script src="js/app.js"></script>

    <script>
        function showResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
            
            // Auto-scroll to bottom
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }

        function testBasicFunctions() {
            showResult('بدء اختبار الوظائف الأساسية...', 'info');
            
            const tests = [
                { name: 'LocalStorage', test: () => typeof localStorage !== 'undefined' },
                { name: 'IndexedDB', test: () => typeof indexedDB !== 'undefined' },
                { name: 'Geolocation API', test: () => 'geolocation' in navigator },
                { name: 'Notification API', test: () => 'Notification' in window },
                { name: 'Service Worker', test: () => 'serviceWorker' in navigator },
                { name: 'WebSocket', test: () => typeof WebSocket !== 'undefined' }
            ];

            let passed = 0;
            tests.forEach(test => {
                try {
                    const result = test.test();
                    if (result) {
                        showResult(`✅ ${test.name}: متاح`, 'success');
                        passed++;
                    } else {
                        showResult(`❌ ${test.name}: غير متاح`, 'error');
                    }
                } catch (error) {
                    showResult(`❌ ${test.name}: خطأ - ${error.message}`, 'error');
                }
            });

            showResult(`اكتمل الاختبار: ${passed}/${tests.length} نجح`, passed === tests.length ? 'success' : 'error');
        }

        function testAuthentication() {
            showResult('بدء اختبار نظام المصادقة...', 'info');
            
            try {
                if (typeof AuthManager !== 'undefined') {
                    showResult('✅ AuthManager: محمل بنجاح', 'success');
                } else {
                    showResult('❌ AuthManager: غير محمل', 'error');
                }

                // Test user creation
                const testUser = {
                    fullName: 'مستخدم تجريبي',
                    phoneNumber: '+970591234567',
                    userRole: 'citizen',
                    locationArea: 'القدس'
                };

                localStorage.setItem('testUser', JSON.stringify(testUser));
                const retrieved = JSON.parse(localStorage.getItem('testUser'));
                
                if (retrieved.fullName === testUser.fullName) {
                    showResult('✅ حفظ واسترجاع بيانات المستخدم: نجح', 'success');
                } else {
                    showResult('❌ حفظ واسترجاع بيانات المستخدم: فشل', 'error');
                }

                localStorage.removeItem('testUser');
                showResult('اكتمل اختبار المصادقة', 'success');
            } catch (error) {
                showResult(`خطأ في اختبار المصادقة: ${error.message}`, 'error');
            }
        }

        function testNotifications() {
            showResult('بدء اختبار الإشعارات...', 'info');
            
            try {
                if ('Notification' in window) {
                    showResult('✅ Notification API: مدعوم', 'success');
                    showResult(`حالة الأذونات: ${Notification.permission}`, 'info');
                } else {
                    showResult('❌ Notification API: غير مدعوم', 'error');
                }

                if (window.enhancedNotificationManager) {
                    showResult('✅ Enhanced Notification Manager: محمل', 'success');
                } else {
                    showResult('❌ Enhanced Notification Manager: غير محمل', 'error');
                }

                showResult('اكتمل اختبار الإشعارات', 'success');
            } catch (error) {
                showResult(`خطأ في اختبار الإشعارات: ${error.message}`, 'error');
            }
        }

        function testSMS() {
            showResult('بدء اختبار خدمة SMS...', 'info');
            
            try {
                if (window.smsService) {
                    showResult('✅ SMS Service: محمل بنجاح', 'success');
                    
                    // Test SMS validation
                    const validPhone = '+970591234567';
                    const invalidPhone = '123';
                    
                    if (window.smsService.validatePhoneNumber && 
                        window.smsService.validatePhoneNumber(validPhone)) {
                        showResult('✅ التحقق من رقم الهاتف: يعمل', 'success');
                    } else {
                        showResult('❌ التحقق من رقم الهاتف: لا يعمل', 'error');
                    }
                } else {
                    showResult('❌ SMS Service: غير محمل', 'error');
                }

                showResult('اكتمل اختبار SMS', 'success');
            } catch (error) {
                showResult(`خطأ في اختبار SMS: ${error.message}`, 'error');
            }
        }

        function testDatabase() {
            showResult('بدء اختبار قاعدة البيانات...', 'info');
            
            try {
                if (window.databaseManager) {
                    showResult('✅ Database Manager: محمل بنجاح', 'success');
                } else {
                    showResult('❌ Database Manager: غير محمل', 'error');
                }

                // Test IndexedDB
                if (typeof indexedDB !== 'undefined') {
                    showResult('✅ IndexedDB: متاح', 'success');
                } else {
                    showResult('❌ IndexedDB: غير متاح', 'error');
                }

                showResult('اكتمل اختبار قاعدة البيانات', 'success');
            } catch (error) {
                showResult(`خطأ في اختبار قاعدة البيانات: ${error.message}`, 'error');
            }
        }

        function testLocation() {
            showResult('بدء اختبار تحديد الموقع...', 'info');
            
            try {
                if ('geolocation' in navigator) {
                    showResult('✅ Geolocation API: متاح', 'success');
                    
                    navigator.geolocation.getCurrentPosition(
                        (position) => {
                            showResult(`✅ تم تحديد الموقع: ${position.coords.latitude.toFixed(4)}, ${position.coords.longitude.toFixed(4)}`, 'success');
                        },
                        (error) => {
                            showResult(`⚠️ خطأ في تحديد الموقع: ${error.message}`, 'error');
                        },
                        { timeout: 5000 }
                    );
                } else {
                    showResult('❌ Geolocation API: غير متاح', 'error');
                }

                if (window.locationManager) {
                    showResult('✅ Location Manager: محمل بنجاح', 'success');
                } else {
                    showResult('❌ Location Manager: غير محمل', 'error');
                }
            } catch (error) {
                showResult(`خطأ في اختبار الموقع: ${error.message}`, 'error');
            }
        }

        function testFullWorkflow() {
            showResult('بدء اختبار التدفق الكامل...', 'info');
            
            try {
                // Test alert creation
                const testAlert = {
                    id: 'test_' + Date.now(),
                    type: 'ambulance',
                    priority: 'critical',
                    customMessage: 'هذا إنذار تجريبي',
                    location: { latitude: 31.7683, longitude: 35.2137 },
                    timestamp: new Date().toISOString(),
                    senderInfo: {
                        fullName: 'مستخدم تجريبي',
                        phoneNumber: '+970591234567',
                        userRole: 'citizen',
                        locationArea: 'القدس'
                    }
                };

                showResult('✅ إنشاء إنذار تجريبي: نجح', 'success');

                // Test notification display
                if (window.enhancedNotificationManager) {
                    setTimeout(() => {
                        window.enhancedNotificationManager.showEmergencyNotification(testAlert);
                        showResult('✅ عرض الإشعار: نجح', 'success');
                    }, 1000);
                }

                showResult('اكتمل اختبار التدفق الكامل', 'success');
            } catch (error) {
                showResult(`خطأ في اختبار التدفق الكامل: ${error.message}`, 'error');
            }
        }

        // Auto-run basic test on load
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                showResult('مرحباً بك في اختبار درع الطوارئ!', 'info');
                showResult('اضغط على الأزرار أعلاه لاختبار الوظائف المختلفة', 'info');
            }, 500);
        });
    </script>
</body>
</html>
