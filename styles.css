/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    overflow-x: hidden;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

.loading-content {
    text-align: center;
    color: white;
}

.shield-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    animation: pulse 2s infinite;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 1rem auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* App Container */
.app {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
    min-height: 100vh;
}

/* Header */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.logo .shield-icon {
    font-size: 2rem;
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2563eb;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-dot.online {
    background: #10b981;
}

.status-dot.offline {
    background: #ef4444;
}

.status-text {
    font-size: 0.875rem;
    font-weight: 500;
}

/* Location Section */
.location-section {
    margin-bottom: 2rem;
}

.location-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.location-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.location-icon {
    font-size: 1.5rem;
}

.location-header h3 {
    flex: 1;
    font-size: 1.125rem;
    font-weight: 600;
}

.refresh-btn {
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: background 0.2s;
}

.refresh-btn:hover {
    background: rgba(0, 0, 0, 0.1);
}

.location-display {
    text-align: center;
}

.location-text {
    margin-bottom: 1rem;
    color: #666;
}

.manual-location-btn {
    background: #3b82f6;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s;
}

.manual-location-btn:hover {
    background: #2563eb;
}

/* Emergency Grid */
.emergency-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.emergency-btn {
    position: relative;
    background: white;
    border: none;
    border-radius: 20px;
    padding: 2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    min-height: 160px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.emergency-btn:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.emergency-btn:active {
    transform: translateY(-2px);
}

.btn-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.btn-text h3 {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.btn-text p {
    font-size: 0.875rem;
    color: #666;
    opacity: 0.8;
}

.btn-pulse {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    border-radius: 20px;
    transform: translate(-50%, -50%);
    opacity: 0;
    pointer-events: none;
}

/* Emergency Button Colors */
.emergency-btn.ambulance {
    border-left: 6px solid #ef4444;
}

.emergency-btn.ambulance:hover .btn-pulse {
    background: rgba(239, 68, 68, 0.1);
    animation: pulse-effect 0.6s ease;
}

.emergency-btn.fire {
    border-left: 6px solid #f97316;
}

.emergency-btn.fire:hover .btn-pulse {
    background: rgba(249, 115, 22, 0.1);
    animation: pulse-effect 0.6s ease;
}

.emergency-btn.civil-defense {
    border-left: 6px solid #3b82f6;
}

.emergency-btn.civil-defense:hover .btn-pulse {
    background: rgba(59, 130, 246, 0.1);
    animation: pulse-effect 0.6s ease;
}

.emergency-btn.settler-attack {
    border-left: 6px solid #dc2626;
}

.emergency-btn.settler-attack:hover .btn-pulse {
    background: rgba(220, 38, 38, 0.1);
    animation: pulse-effect 0.6s ease;
}

.emergency-btn.settlers-present {
    border-left: 6px solid #f59e0b;
}

.emergency-btn.settlers-present:hover .btn-pulse {
    background: rgba(245, 158, 11, 0.1);
    animation: pulse-effect 0.6s ease;
}

.emergency-btn.intrusion {
    border-left: 6px solid #7c3aed;
}

.emergency-btn.intrusion:hover .btn-pulse {
    background: rgba(124, 58, 237, 0.1);
    animation: pulse-effect 0.6s ease;
}

@keyframes pulse-effect {
    0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
    50% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
    100% { opacity: 0; transform: translate(-50%, -50%) scale(1.2); }
}

/* Quick Actions */
.quick-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
}

.action-btn {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: none;
    border-radius: 12px;
    padding: 1rem 1.5rem;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.action-btn span {
    font-size: 1.25rem;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 1rem;
}

.modal-content {
    background: white;
    border-radius: 20px;
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease;
}

.modal-content.large {
    max-width: 600px;
}

.modal-content.success {
    text-align: center;
    padding: 2rem;
}

.success-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 1.5rem 0;
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 1.5rem;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: background 0.2s;
}

.close-btn:hover {
    background: rgba(0, 0, 0, 0.1);
}

.modal-body {
    padding: 0 1.5rem 1.5rem;
}

.alert-preview {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f9fafb;
    border-radius: 12px;
    margin-bottom: 1.5rem;
}

.alert-icon {
    font-size: 2rem;
}

.alert-details h4 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.alert-details p {
    font-size: 0.875rem;
    color: #666;
}

.modal-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.btn-primary, .btn-secondary {
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
}

.btn-primary {
    background: #dc2626;
    color: white;
}

.btn-primary:hover {
    background: #b91c1c;
}

.btn-secondary {
    background: #f3f4f6;
    color: #374151;
}

.btn-secondary:hover {
    background: #e5e7eb;
}

/* Responsive Design */
@media (max-width: 768px) {
    .app {
        padding: 0.5rem;
    }
    
    .emergency-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .emergency-btn {
        padding: 1.5rem;
        min-height: 140px;
    }
    
    .btn-icon {
        font-size: 2.5rem;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .quick-actions {
        flex-direction: column;
    }
    
    .action-btn {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .logo h1 {
        font-size: 1.25rem;
    }
    
    .emergency-btn {
        padding: 1rem;
        min-height: 120px;
    }
    
    .btn-icon {
        font-size: 2rem;
    }
    
    .btn-text h3 {
        font-size: 1rem;
    }
    
    .modal-content {
        margin: 0.5rem;
    }
}

/* Custom Message Styles */
.custom-message-section {
    margin: 1.5rem 0;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.custom-message-section h4 {
    margin: 0 0 1rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: #374151;
}

.custom-message-section h5 {
    margin: 1rem 0 0.5rem 0;
    font-size: 0.875rem;
    font-weight: 600;
    color: #4b5563;
}

#custom-message {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 0.875rem;
    font-family: inherit;
    resize: vertical;
    min-height: 80px;
    transition: border-color 0.2s;
}

#custom-message:focus {
    outline: none;
    border-color: #3b82f6;
}

.message-counter {
    text-align: left;
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: #6b7280;
}

.quick-templates {
    margin-top: 1rem;
}

.template-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.template-btn {
    background: #e0e7ff;
    color: #3730a3;
    border: 1px solid #c7d2fe;
    border-radius: 6px;
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s;
}

.template-btn:hover {
    background: #c7d2fe;
    transform: translateY(-1px);
}

/* Sender Information Styles */
.sender-info-section {
    margin: 1.5rem 0;
    padding: 1rem;
    background: #fef7f0;
    border-radius: 12px;
    border: 1px solid #fed7aa;
}

.sender-info-section h4 {
    margin: 0 0 1rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: #ea580c;
}

.sender-details {
    display: grid;
    gap: 0.5rem;
}

.sender-detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: white;
    border-radius: 6px;
    font-size: 0.875rem;
}

.sender-detail-label {
    font-weight: 600;
    color: #374151;
}

.sender-detail-value {
    color: #6b7280;
    font-family: monospace;
}

/* Enhanced Alert Preview */
.alert-preview {
    background: #f1f5f9;
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1rem;
    border-right: 4px solid #e2e8f0;
}

.alert-preview.critical {
    background: #fef2f2;
    border-right-color: #dc2626;
}

.alert-preview.high {
    background: #fffbeb;
    border-right-color: #f59e0b;
}

.alert-preview.medium {
    background: #eff6ff;
    border-right-color: #3b82f6;
}

/* Responsive adjustments for custom messages */
@media (max-width: 768px) {
    .modal-content.large {
        max-width: 95vw;
        margin: 0.5rem;
    }

    .template-buttons {
        flex-direction: column;
    }

    .template-btn {
        text-align: center;
    }

    .sender-detail-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
}
