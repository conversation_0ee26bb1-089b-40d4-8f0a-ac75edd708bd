#!/usr/bin/env python3
"""
Emergency Shield - Quick Server Starter
تشغيل سريع لخادم درع الطوارئ
"""

import http.server
import socketserver
import webbrowser
import os
import sys
import threading
import time
from urllib.parse import urlparse

class EmergencyShieldHandler(http.server.SimpleHTTPRequestHandler):
    """Custom handler for Emergency Shield with proper MIME types and CORS"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.getcwd(), **kwargs)
    
    def end_headers(self):
        # Add CORS headers
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        
        # Add security headers
        self.send_header('X-Content-Type-Options', 'nosniff')
        self.send_header('X-Frame-Options', 'DENY')
        self.send_header('X-XSS-Protection', '1; mode=block')
        
        super().end_headers()
    
    def guess_type(self, path):
        """Enhanced MIME type detection"""
        mimetype, encoding = super().guess_type(path)
        
        # Custom MIME types for Emergency Shield
        if path.endswith('.js'):
            return 'application/javascript'
        elif path.endswith('.css'):
            return 'text/css'
        elif path.endswith('.html'):
            return 'text/html; charset=utf-8'
        elif path.endswith('.json'):
            return 'application/json'
        elif path.endswith('.svg'):
            return 'image/svg+xml'
        elif path.endswith('.woff2'):
            return 'font/woff2'
        elif path.endswith('.woff'):
            return 'font/woff'
        
        return mimetype
    
    def do_OPTIONS(self):
        """Handle CORS preflight requests"""
        self.send_response(200)
        self.end_headers()
    
    def log_message(self, format, *args):
        """Custom logging with Arabic support"""
        message = format % args
        print(f"[{time.strftime('%H:%M:%S')}] {message}")

def check_files():
    """Check if all required files exist"""
    required_files = [
        'index.html',
        'admin.html', 
        'auth.html',
        'styles.css',
        'config.js',
        'fix-errors.js'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ الملفات المفقودة:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    print("✅ جميع الملفات الأساسية موجودة")
    return True

def find_free_port(start_port=8000):
    """Find a free port starting from start_port"""
    import socket
    
    for port in range(start_port, start_port + 100):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    
    return None

def open_browser_delayed(url, delay=2):
    """Open browser after a delay"""
    def open_browser():
        time.sleep(delay)
        print(f"🌐 فتح المتصفح: {url}")
        webbrowser.open(url)
    
    thread = threading.Thread(target=open_browser)
    thread.daemon = True
    thread.start()

def print_banner():
    """Print application banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                        🛡️ درع الطوارئ                        ║
    ║                    Emergency Shield v2.0                    ║
    ║                                                              ║
    ║              نظام إنذار ذكي وفعال للطوارئ                   ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def print_urls(port):
    """Print available URLs"""
    base_url = f"http://localhost:{port}"
    
    print("\n📱 الروابط المتاحة:")
    print("=" * 60)
    print(f"🏠 التطبيق الرئيسي:     {base_url}")
    print(f"🔐 صفحة المصادقة:      {base_url}/auth.html")
    print(f"🛡️ لوحة التحكم:        {base_url}/admin.html")
    print(f"🔔 عرض الإشعارات:      {base_url}/notifications-demo.html")
    print(f"🧪 اختبار سريع:        {base_url}/quick-test.html")
    print(f"📱 اختبار رمز التحقق:   {base_url}/test-sms.html")
    print(f"✅ فحص نهائي:          {base_url}/final-check.html")
    print("=" * 60)

def print_instructions():
    """Print usage instructions"""
    print("\n📋 تعليمات الاستخدام:")
    print("=" * 60)
    print("1. 🔐 ابدأ بالتسجيل في صفحة المصادقة")
    print("2. 📱 استخدم رقم هاتف صحيح (+970xxxxxxxxx)")
    print("3. 🔢 أدخل أي رمز تحقق (6 أرقام)")
    print("4. 🚨 ابدأ في إرسال الإنذارات")
    print("5. 🛡️ استخدم لوحة التحكم (admin/admin123)")
    print("=" * 60)

def print_tips():
    """Print helpful tips"""
    print("\n💡 نصائح مفيدة:")
    print("=" * 60)
    print("• اسمح للمتصفح بالوصول للموقع الجغرافي")
    print("• اسمح بالإشعارات للحصول على تجربة كاملة")
    print("• استخدم HTTPS للميزات المتقدمة")
    print("• جرب صفحة الاختبار السريع للتأكد من عمل كل شيء")
    print("=" * 60)

def main():
    """Main function"""
    print_banner()
    
    # Check if files exist
    if not check_files():
        print("\n❌ لا يمكن تشغيل الخادم - ملفات مفقودة")
        sys.exit(1)
    
    # Find free port
    port = find_free_port()
    if not port:
        print("❌ لا يمكن العثور على منفذ متاح")
        sys.exit(1)
    
    try:
        # Create server
        with socketserver.TCPServer(("", port), EmergencyShieldHandler) as httpd:
            print(f"✅ تم تشغيل الخادم على المنفذ {port}")
            
            # Print information
            print_urls(port)
            print_instructions()
            print_tips()
            
            # Open browser
            open_browser_delayed(f"http://localhost:{port}")
            
            print(f"\n🚀 الخادم يعمل... اضغط Ctrl+C للإيقاف")
            print("=" * 60)
            
            # Start server
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n\n🛑 تم إيقاف الخادم")
        print("شكراً لاستخدام درع الطوارئ!")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الخادم: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
