#!/bin/bash

# Emergency Shield App Builder
# بناء تطبيق درع الطوارئ

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Print banner
print_banner() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    🛠️ بناء تطبيق درع الطوارئ                  ║"
    echo "║                Emergency Shield App Builder                  ║"
    echo "║                                                              ║"
    echo "║                     تحويل إلى تطبيق سطح المكتب                ║"
    echo "║                                                              ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check requirements
check_requirements() {
    echo -e "${YELLOW}🔍 فحص المتطلبات...${NC}"
    
    if ! command_exists node; then
        echo -e "${RED}❌ Node.js غير مثبت${NC}"
        echo -e "${YELLOW}📥 يرجى تثبيت Node.js من: https://nodejs.org/${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Node.js مثبت${NC}"
    node --version
    
    if ! command_exists npm; then
        echo -e "${RED}❌ npm غير متاح${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ npm متاح${NC}"
    npm --version
    echo
}

# Install dependencies
install_dependencies() {
    echo -e "${BLUE}📦 تثبيت المتطلبات...${NC}"
    echo "════════════════════════════════════════════════════════════════"
    
    echo -e "${CYAN}🔄 تثبيت Electron...${NC}"
    if ! npm install electron --save-dev; then
        echo -e "${RED}❌ فشل في تثبيت Electron${NC}"
        exit 1
    fi
    
    echo -e "${CYAN}🔄 تثبيت Electron Builder...${NC}"
    if ! npm install electron-builder --save-dev; then
        echo -e "${RED}❌ فشل في تثبيت Electron Builder${NC}"
        exit 1
    fi
    
    echo -e "${CYAN}🔄 تثبيت المكونات الإضافية...${NC}"
    if ! npm install electron-updater electron-log electron-store node-notifier --save; then
        echo -e "${RED}❌ فشل في تثبيت المكونات الإضافية${NC}"
        exit 1
    fi
    
    echo
    echo -e "${GREEN}✅ تم تثبيت جميع المتطلبات بنجاح${NC}"
    echo
}

# Prepare build
prepare_build() {
    echo -e "${PURPLE}🏗️ تحضير البناء...${NC}"
    
    # Create icons directory if it doesn't exist
    if [ ! -d "icons" ]; then
        mkdir -p icons
        echo -e "${BLUE}📁 تم إنشاء مجلد الأيقونات${NC}"
    fi
    
    # Check for icon files
    if [ ! -f "icons/icon.png" ]; then
        echo -e "${YELLOW}⚠️ يرجى إضافة ملف icon.png في مجلد icons${NC}"
    fi
    
    if [ ! -f "icons/icon.ico" ]; then
        echo -e "${YELLOW}⚠️ يرجى إضافة ملف icon.ico في مجلد icons (للويندوز)${NC}"
    fi
    
    if [ ! -f "icons/icon.icns" ]; then
        echo -e "${YELLOW}⚠️ يرجى إضافة ملف icon.icns في مجلد icons (للماك)${NC}"
    fi
}

# Build application
build_app() {
    echo -e "${GREEN}🚀 بدء عملية البناء...${NC}"
    echo "════════════════════════════════════════════════════════════════"
    
    # Determine platform
    case "$(uname -s)" in
        Linux*)
            echo -e "${CYAN}🐧 بناء إصدار Linux...${NC}"
            if npm run build -- --linux; then
                echo -e "${GREEN}✅ تم بناء إصدار Linux بنجاح${NC}"
            else
                echo -e "${RED}❌ فشل في بناء إصدار Linux${NC}"
            fi
            ;;
        Darwin*)
            echo -e "${CYAN}🍎 بناء إصدار macOS...${NC}"
            if npm run build -- --mac; then
                echo -e "${GREEN}✅ تم بناء إصدار macOS بنجاح${NC}"
            else
                echo -e "${RED}❌ فشل في بناء إصدار macOS${NC}"
            fi
            ;;
        CYGWIN*|MINGW*|MSYS*)
            echo -e "${CYAN}🪟 بناء إصدار Windows...${NC}"
            if npm run build-win; then
                echo -e "${GREEN}✅ تم بناء إصدار Windows بنجاح${NC}"
            else
                echo -e "${RED}❌ فشل في بناء إصدار Windows${NC}"
            fi
            ;;
        *)
            echo -e "${YELLOW}⚠️ نظام تشغيل غير معروف، محاولة البناء العام...${NC}"
            if npm run build; then
                echo -e "${GREEN}✅ تم البناء بنجاح${NC}"
            else
                echo -e "${RED}❌ فشل في البناء${NC}"
            fi
            ;;
    esac
    
    # Build portable version
    echo
    echo -e "${CYAN}🔨 بناء إصدار محمول...${NC}"
    if npm run pack; then
        echo -e "${GREEN}✅ تم بناء الإصدار المحمول بنجاح${NC}"
    else
        echo -e "${RED}❌ فشل في بناء الإصدار المحمول${NC}"
    fi
}

# Show results
show_results() {
    echo
    echo -e "${WHITE}🎉 اكتملت عملية البناء!${NC}"
    echo "════════════════════════════════════════════════════════════════"
    
    if [ -d "dist" ]; then
        echo -e "${BLUE}📁 ملفات التطبيق متاحة في مجلد: dist/${NC}"
        echo
        echo -e "${PURPLE}📋 الملفات المنشأة:${NC}"
        ls -la dist/ 2>/dev/null | grep -E '\.(exe|dmg|AppImage|deb|rpm|tar\.gz)$' || echo "لا توجد ملفات تطبيق"
        echo
        
        echo -e "${GREEN}💡 أنواع الملفات:${NC}"
        echo "• ملفات .exe - مثبت Windows"
        echo "• ملفات .dmg - مثبت macOS"
        echo "• ملفات .AppImage - تطبيق Linux محمول"
        echo "• ملفات .deb - حزمة Debian/Ubuntu"
        echo "• ملفات .rpm - حزمة Red Hat/Fedora"
        echo
        
        echo -e "${CYAN}🚀 لتشغيل التطبيق:${NC}"
        echo "1. ثبت الملف المناسب لنظام التشغيل"
        echo "2. أو شغل الإصدار المحمول مباشرة"
        echo
        
        read -p "هل تريد فتح مجلد الملفات؟ (y/n): " open_folder
        if [[ $open_folder =~ ^[Yy]$ ]]; then
            if command_exists xdg-open; then
                xdg-open dist/
            elif command_exists open; then
                open dist/
            else
                echo -e "${YELLOW}⚠️ لا يمكن فتح المجلد تلقائياً${NC}"
            fi
        fi
    else
        echo -e "${RED}❌ لم يتم العثور على مجلد dist${NC}"
        echo "تأكد من نجاح عملية البناء"
    fi
}

# Show help
show_help() {
    echo -e "${WHITE}📞 للدعم الفني:${NC}"
    echo "• راجع ملف README.md"
    echo "• تحقق من ملف package.json"
    echo "• تأكد من وجود جميع الملفات المطلوبة"
    echo "• تأكد من تثبيت Node.js و npm"
    echo
}

# Cleanup function
cleanup() {
    echo -e "\n${YELLOW}🧹 تنظيف الملفات المؤقتة...${NC}"
    # Add cleanup commands if needed
}

# Main function
main() {
    # Set up signal handlers
    trap cleanup SIGINT SIGTERM
    
    # Print banner
    print_banner
    
    # Check requirements
    check_requirements
    
    # Install dependencies
    install_dependencies
    
    # Prepare build
    prepare_build
    
    # Build application
    build_app
    
    # Show results
    show_results
    
    # Show help
    show_help
}

# Make script executable
chmod +x "$0" 2>/dev/null

# Run main function
main "$@"
