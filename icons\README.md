# Icons Directory

This directory should contain the following icon files for the Emergency Shield PWA:

## Required Icons:
- `icon-72x72.png` - 72x72 pixels
- `icon-96x96.png` - 96x96 pixels  
- `icon-128x128.png` - 128x128 pixels
- `icon-144x144.png` - 144x144 pixels
- `icon-152x152.png` - 152x152 pixels
- `icon-192x192.png` - 192x192 pixels
- `icon-384x384.png` - 384x384 pixels
- `icon-512x512.png` - 512x512 pixels
- `favicon-16x16.png` - 16x16 pixels
- `favicon-32x32.png` - 32x32 pixels
- `apple-touch-icon.png` - 180x180 pixels
- `badge-72x72.png` - 72x72 pixels (for notifications)

## Shortcut Icons:
- `ambulance-96x96.png` - Ambulance shortcut icon
- `fire-96x96.png` - Fire shortcut icon
- `civil-defense-96x96.png` - Civil defense shortcut icon
- `warning-96x96.png` - Warning shortcut icon

## Design Guidelines:
- Use the shield emoji (🛡️) as the main icon
- Colors: Primary blue (#1e3a8a), Emergency red (#dc2626)
- Ensure icons are clear and recognizable at all sizes
- Use high contrast for better visibility
- Consider Arabic text if needed for shortcuts

## Tools for Icon Generation:
- Use online PWA icon generators
- Adobe Illustrator or similar vector tools
- Canva or similar design tools
- Ensure all icons are optimized for web use
