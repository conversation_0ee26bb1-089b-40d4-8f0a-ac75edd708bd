// Notifications and Alerts Management
class NotificationManager {
    constructor() {
        this.permission = 'default';
        this.isSupported = 'Notification' in window;
        this.soundEnabled = true;
        this.vibrationEnabled = true;
        this.notificationQueue = [];
        
        this.init();
    }

    async init() {
        if (this.isSupported) {
            this.permission = Notification.permission;
            
            // Load settings from localStorage
            this.loadSettings();
            
            // Request permission if not already granted
            if (this.permission === 'default') {
                await this.requestPermission();
            }
        }
    }

    // Request notification permission
    async requestPermission() {
        if (!this.isSupported) {
            throw new Error('Notifications are not supported in this browser');
        }

        try {
            this.permission = await Notification.requestPermission();
            return this.permission;
        } catch (error) {
            console.error('Permission request error:', error);
            return 'denied';
        }
    }

    // Send notification
    async sendNotification(options) {
        if (!this.isSupported || this.permission !== 'granted') {
            console.warn('Notifications not available or permission denied');
            return null;
        }

        const defaultOptions = {
            icon: '/icons/icon-192x192.png',
            badge: '/icons/badge-72x72.png',
            dir: 'rtl',
            lang: 'ar',
            requireInteraction: true,
            silent: !this.soundEnabled
        };

        const finalOptions = { ...defaultOptions, ...options };

        try {
            const notification = new Notification(finalOptions.title, finalOptions);
            
            // Add click handler
            notification.onclick = () => {
                window.focus();
                if (finalOptions.onClick) {
                    finalOptions.onClick();
                }
                notification.close();
            };

            // Auto close after 10 seconds if not requiring interaction
            if (!finalOptions.requireInteraction) {
                setTimeout(() => {
                    notification.close();
                }, 10000);
            }

            // Play sound if enabled
            if (this.soundEnabled && !finalOptions.silent) {
                this.playNotificationSound(finalOptions.urgency || 'normal');
            }

            // Vibrate if enabled and supported
            if (this.vibrationEnabled && 'vibrate' in navigator) {
                this.vibrateDevice(finalOptions.urgency || 'normal');
            }

            return notification;
        } catch (error) {
            console.error('Notification error:', error);
            return null;
        }
    }

    // Send emergency alert notification
    async sendEmergencyAlert(alertType, location) {
        const alertConfigs = {
            'ambulance': {
                title: '🆘 طلب إسعاف عاجل',
                body: 'تم إرسال طلب إسعاف من موقعك',
                urgency: 'critical',
                tag: 'ambulance'
            },
            'fire': {
                title: '🔥 إنذار حريق',
                body: 'تم إرسال إنذار حريق من موقعك',
                urgency: 'critical',
                tag: 'fire'
            },
            'civil-defense': {
                title: '🛡️ طلب دفاع مدني',
                body: 'تم إرسال طلب دفاع مدني من موقعك',
                urgency: 'high',
                tag: 'civil-defense'
            },
            'settler-attack': {
                title: '⚠️ إنذار هجوم مستوطنين',
                body: 'تم إرسال إنذار هجوم مستوطنين من موقعك',
                urgency: 'critical',
                tag: 'settler-attack'
            },
            'settlers-present': {
                title: '🚷 تنبيه وجود مستوطنين',
                body: 'تم إرسال تنبيه وجود مستوطنين في الموقع',
                urgency: 'high',
                tag: 'settlers-present'
            },
            'intrusion': {
                title: '🚨 إنذار اقتحام',
                body: 'تم إرسال إنذار اقتحام من موقعك',
                urgency: 'critical',
                tag: 'intrusion'
            }
        };

        const config = alertConfigs[alertType];
        if (!config) {
            throw new Error('Unknown alert type');
        }

        const options = {
            ...config,
            requireInteraction: true,
            actions: [
                {
                    action: 'view',
                    title: 'عرض التفاصيل'
                },
                {
                    action: 'close',
                    title: 'إغلاق'
                }
            ],
            data: {
                alertType,
                location,
                timestamp: new Date().toISOString()
            }
        };

        return await this.sendNotification(options);
    }

    // Send incoming alert notification (for received alerts)
    async sendIncomingAlert(alert) {
        const alertConfigs = {
            'ambulance': '🆘 طلب إسعاف في المنطقة',
            'fire': '🔥 حريق في المنطقة',
            'civil-defense': '🛡️ حالة طوارئ في المنطقة',
            'settler-attack': '⚠️ هجوم مستوطنين في المنطقة',
            'settlers-present': '🚷 مستوطنين في المنطقة',
            'intrusion': '🚨 اقتحام في المنطقة'
        };

        const title = alertConfigs[alert.type] || '🚨 تنبيه طوارئ';
        const distance = alert.distance ? `على بعد ${Math.round(alert.distance)} متر` : '';
        
        const options = {
            title,
            body: `${alert.description || ''} ${distance}`,
            urgency: 'critical',
            requireInteraction: true,
            tag: `incoming-${alert.type}`,
            actions: [
                {
                    action: 'navigate',
                    title: 'الذهاب للموقع'
                },
                {
                    action: 'acknowledge',
                    title: 'تم الاستلام'
                }
            ],
            data: alert
        };

        return await this.sendNotification(options);
    }

    // Play notification sound
    playNotificationSound(urgency = 'normal') {
        try {
            const audio = new Audio();
            
            switch (urgency) {
                case 'critical':
                    audio.src = '/sounds/critical-alert.mp3';
                    break;
                case 'high':
                    audio.src = '/sounds/high-alert.mp3';
                    break;
                default:
                    audio.src = '/sounds/normal-alert.mp3';
                    break;
            }

            audio.volume = 0.8;
            audio.play().catch(error => {
                console.warn('Could not play notification sound:', error);
            });
        } catch (error) {
            console.warn('Audio not supported:', error);
        }
    }

    // Vibrate device
    vibrateDevice(urgency = 'normal') {
        if (!('vibrate' in navigator)) return;

        let pattern;
        switch (urgency) {
            case 'critical':
                pattern = [200, 100, 200, 100, 200, 100, 200];
                break;
            case 'high':
                pattern = [300, 100, 300];
                break;
            default:
                pattern = [200];
                break;
        }

        navigator.vibrate(pattern);
    }

    // Show browser notification fallback
    showBrowserAlert(title, message, type = 'info') {
        // Create custom notification element
        const notification = document.createElement('div');
        notification.className = `browser-notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-icon">${this.getTypeIcon(type)}</div>
                <div class="notification-text">
                    <h4>${title}</h4>
                    <p>${message}</p>
                </div>
                <button class="notification-close">✕</button>
            </div>
        `;

        // Add styles if not already added
        this.addNotificationStyles();

        // Add to page
        document.body.appendChild(notification);

        // Add close functionality
        notification.querySelector('.notification-close').onclick = () => {
            notification.remove();
        };

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);

        return notification;
    }

    getTypeIcon(type) {
        const icons = {
            'success': '✅',
            'error': '❌',
            'warning': '⚠️',
            'info': 'ℹ️',
            'emergency': '🚨'
        };
        return icons[type] || 'ℹ️';
    }

    addNotificationStyles() {
        if (document.getElementById('notification-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'notification-styles';
        styles.textContent = `
            .browser-notification {
                position: fixed;
                top: 20px;
                right: 20px;
                background: white;
                border-radius: 12px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
                z-index: 10000;
                max-width: 400px;
                animation: slideInRight 0.3s ease;
            }
            
            .browser-notification.emergency {
                border-left: 4px solid #dc2626;
            }
            
            .browser-notification.warning {
                border-left: 4px solid #f59e0b;
            }
            
            .browser-notification.success {
                border-left: 4px solid #10b981;
            }
            
            .browser-notification.error {
                border-left: 4px solid #ef4444;
            }
            
            .notification-content {
                display: flex;
                align-items: flex-start;
                padding: 1rem;
                gap: 0.75rem;
            }
            
            .notification-icon {
                font-size: 1.5rem;
                flex-shrink: 0;
            }
            
            .notification-text {
                flex: 1;
            }
            
            .notification-text h4 {
                margin: 0 0 0.25rem 0;
                font-size: 1rem;
                font-weight: 600;
            }
            
            .notification-text p {
                margin: 0;
                font-size: 0.875rem;
                color: #666;
            }
            
            .notification-close {
                background: none;
                border: none;
                font-size: 1.25rem;
                cursor: pointer;
                padding: 0.25rem;
                border-radius: 4px;
                flex-shrink: 0;
            }
            
            .notification-close:hover {
                background: rgba(0, 0, 0, 0.1);
            }
            
            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
        `;
        document.head.appendChild(styles);
    }

    // Settings management
    saveSettings() {
        const settings = {
            soundEnabled: this.soundEnabled,
            vibrationEnabled: this.vibrationEnabled
        };
        localStorage.setItem('notificationSettings', JSON.stringify(settings));
    }

    loadSettings() {
        try {
            const stored = localStorage.getItem('notificationSettings');
            if (stored) {
                const settings = JSON.parse(stored);
                this.soundEnabled = settings.soundEnabled !== false;
                this.vibrationEnabled = settings.vibrationEnabled !== false;
            }
        } catch (error) {
            console.error('Failed to load notification settings:', error);
        }
    }

    // Toggle settings
    toggleSound() {
        this.soundEnabled = !this.soundEnabled;
        this.saveSettings();
        return this.soundEnabled;
    }

    toggleVibration() {
        this.vibrationEnabled = !this.vibrationEnabled;
        this.saveSettings();
        return this.vibrationEnabled;
    }

    // Get notification status
    getStatus() {
        return {
            supported: this.isSupported,
            permission: this.permission,
            soundEnabled: this.soundEnabled,
            vibrationEnabled: this.vibrationEnabled
        };
    }
}

// Create global instance
window.notificationManager = new NotificationManager();

// Handle notification actions
if ('serviceWorker' in navigator) {
    navigator.serviceWorker.addEventListener('message', event => {
        if (event.data && event.data.type === 'NOTIFICATION_ACTION') {
            const { action, notification } = event.data;
            
            switch (action) {
                case 'view':
                    window.focus();
                    // Handle view action
                    break;
                case 'navigate':
                    // Handle navigation to alert location
                    if (notification.data && notification.data.location) {
                        const { latitude, longitude } = notification.data.location;
                        const url = `https://maps.google.com/maps?q=${latitude},${longitude}`;
                        window.open(url, '_blank');
                    }
                    break;
                case 'acknowledge':
                    // Handle acknowledgment
                    console.log('Alert acknowledged');
                    break;
            }
        }
    });
}
