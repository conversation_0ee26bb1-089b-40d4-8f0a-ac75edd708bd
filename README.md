# درع الطوارئ - Emergency Shield

## نظام إنذار ذكي للطوارئ

**درع الطوارئ** هو تطبيق ويب متقدم مصمم لإرسال إنذارات الطوارئ الفورية بضغطة زر واحدة. يدعم التطبيق اللغة العربية بالكامل ويعمل على جميع الأجهزة (الهواتف الذكية، الأجهزة اللوحية، أجهزة الكمبيوتر).

## ✨ المميزات الرئيسية

### 🚨 أنواع الإنذارات
- **إسعاف** - حالات طبية طارئة
- **حريق** - إنذارات الحريق
- **دفاع مدني** - طوارئ عامة
- **هجوم مستوطنين** - تنبيهات أمنية
- **مستوطنين في الموقع** - تحذيرات وقائية
- **إنذار اقتحام** - تنبيهات اقتحام

### 📍 تحديد المواقع
- تحديد الموقع التلقائي باستخدام GPS
- تحديد الموقع اليدوي
- عرض الإحداثيات بدقة
- حفظ تاريخ المواقع

### 🔔 نظام الإشعارات
- إشعارات فورية في الوقت الفعلي
- دعم الإشعارات الصوتية
- اهتزاز الجهاز للتنبيهات الحرجة
- إشعارات حتى عند إغلاق التطبيق

### 🛡️ لوحة تحكم المسؤولين
- مراقبة الإنذارات في الوقت الفعلي
- إدارة المستخدمين والصلاحيات
- إحصائيات مفصلة
- خريطة تفاعلية للإنذارات
- إعدادات النظام المتقدمة

### 📱 تطبيق ويب تقدمي (PWA)
- يعمل بدون إنترنت
- قابل للتثبيت على الجهاز
- سرعة تحميل فائقة
- تحديثات تلقائية

## 🚀 التقنيات المستخدمة

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **PWA**: Service Workers, Web App Manifest
- **التخزين**: LocalStorage, IndexedDB
- **الخرائط**: Geolocation API, OpenStreetMap
- **الإشعارات**: Web Notifications API, Push API
- **التصميم**: CSS Grid, Flexbox, Responsive Design

## 📋 متطلبات النظام

### للمستخدمين:
- متصفح حديث يدعم JavaScript
- إذن الوصول للموقع الجغرافي
- إذن الإشعارات (اختياري)

### للمطورين:
- خادم ويب (Apache, Nginx, أو خادم محلي)
- HTTPS (مطلوب للـ PWA والإشعارات)

## 🛠️ التثبيت والإعداد

### 1. تحميل الملفات
```bash
git clone [repository-url]
cd emergency-shield
```

### 2. إعداد الخادم
```bash
# باستخدام Python (للتطوير)
python -m http.server 8000

# أو باستخدام Node.js
npx serve .

# أو باستخدام PHP
php -S localhost:8000
```

### 3. الوصول للتطبيق
افتح المتصفح وانتقل إلى:
```
http://localhost:8000
```

### 4. لوحة تحكم المسؤولين
للوصول للوحة التحكم:
```
http://localhost:8000/admin.html
```

**بيانات الدخول الافتراضية:**
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

## 📁 هيكل المشروع

```
emergency-shield/
├── index.html              # الصفحة الرئيسية
├── admin.html              # لوحة تحكم المسؤولين
├── styles.css              # تنسيقات التطبيق الرئيسي
├── admin-styles.css        # تنسيقات لوحة التحكم
├── manifest.json           # ملف PWA
├── sw.js                   # Service Worker
├── js/
│   ├── app.js              # التطبيق الرئيسي
│   ├── location.js         # إدارة المواقع
│   ├── alerts.js           # إدارة الإنذارات
│   ├── notifications.js    # إدارة الإشعارات
│   └── admin.js            # لوحة تحكم المسؤولين
├── icons/                  # أيقونات التطبيق
└── README.md               # هذا الملف
```

## 🎯 كيفية الاستخدام

### للمستخدمين العاديين:

1. **فتح التطبيق**: انتقل إلى رابط التطبيق
2. **السماح بالموقع**: اقبل طلب الوصول للموقع الجغرافي
3. **إرسال إنذار**: اضغط على نوع الإنذار المناسب
4. **تأكيد الإرسال**: راجع التفاصيل واضغط "إرسال الإنذار"

### للمسؤولين:

1. **تسجيل الدخول**: ادخل بيانات المسؤول
2. **مراقبة الإنذارات**: تابع الإنذارات الواردة
3. **إدارة النظام**: استخدم الإعدادات لتخصيص النظام
4. **تصدير البيانات**: احفظ نسخة احتياطية من البيانات

## ⚙️ الإعدادات والتخصيص

### إعدادات الإشعارات:
- تفعيل/إلغاء الإشعارات التلقائية
- تفعيل/إلغاء الأصوات
- تفعيل/إلغاء الاهتزاز

### إعدادات الموقع:
- نطاق الإنذار (بالمتر)
- دقة تحديد الموقع
- حفظ تاريخ المواقع

### إعدادات الأمان:
- تأكيد الإنذارات قبل الإرسال
- تسجيل جميع الأنشطة
- تشفير البيانات الحساسة

## 🔒 الأمان والخصوصية

- جميع البيانات محفوظة محلياً على الجهاز
- لا يتم إرسال بيانات شخصية للخوادم الخارجية
- تشفير البيانات الحساسة
- إمكانية مسح البيانات في أي وقت

## 🌐 دعم المتصفحات

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 11+
- ✅ Edge 79+
- ✅ Opera 47+

## 📱 دعم الأجهزة

- ✅ Android 5.0+
- ✅ iOS 11+
- ✅ Windows 10+
- ✅ macOS 10.12+
- ✅ Linux (جميع التوزيعات الحديثة)

## 🤝 المساهمة في المشروع

نرحب بمساهماتكم لتطوير المشروع:

1. Fork المشروع
2. إنشاء فرع جديد للميزة
3. إضافة التحسينات
4. إرسال Pull Request

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:

- إنشاء Issue في GitHub
- التواصل عبر البريد الإلكتروني
- مراجعة الوثائق

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## 🙏 شكر وتقدير

- شكر خاص لجميع المساهمين في المشروع
- شكر للمجتمعات مفتوحة المصدر
- شكر لمستخدمي التطبيق وملاحظاتهم القيمة

---

**درع الطوارئ - لأن الأمان حق، وليس رفاهية** 🛡️
