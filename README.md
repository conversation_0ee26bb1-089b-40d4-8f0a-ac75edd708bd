# درع الطوارئ - Emergency Shield

## نظام إنذار ذكي وفعال للطوارئ

**درع الطوارئ** هو تطبيق ويب متقدم وحقيقي مصمم لإرسال إنذارات الطوارئ الفورية مع رسائل مخصصة وتتبع كامل للمستخدمين. يدعم التطبيق اللغة العربية بالكامل ويعمل على جميع الأجهزة مع نظام مصادقة متقدم عبر SMS.

## ✨ المميزات الرئيسية

### 🚨 أنواع الإنذارات
- **إسعاف** - حالات طبية طارئة مع رسائل مخصصة
- **حريق** - إنذارات الحريق مع تفاصيل الموقع
- **دفاع مدني** - طوارئ عامة مع معلومات المرسل
- **هجوم مستوطنين** - تنبيهات أمنية فورية
- **مستوطنين في الموقع** - تحذيرات وقائية
- **إنذار اقتحام** - تنبيهات اقتحام مع تتبع IP

### 📱 نظام المصادقة المتقدم
- **تسجيل بالهاتف** - تسجيل آمن برقم الهاتف
- **تحقق SMS** - رمز تحقق عبر رسالة نصية
- **ملفات المستخدمين** - معلومات كاملة مع تتبع IP
- **أنواع الحسابات** - مواطن، طوارئ، أمن، طبي، إطفاء، مسؤول

### 📍 تحديد المواقع
- تحديد الموقع التلقائي باستخدام GPS
- تحديد الموقع اليدوي
- عرض الإحداثيات بدقة
- حفظ تاريخ المواقع

### 💬 الرسائل المخصصة
- **كتابة رسائل** - إمكانية إضافة رسالة مخصصة مع كل إنذار
- **قوالب سريعة** - رسائل جاهزة للاستخدام السريع
- **عرض تفاصيل المرسل** - اسم، هاتف، منطقة، IP للمسؤولين
- **إرسال SMS** - إشعارات نصية لجميع المشتركين القريبين

### 🔔 نظام الإشعارات المتقدم
- **إشعارات مرئية مميزة** - عرض جميل مع تفاصيل كاملة
- **أصوات متدرجة** - أصوات مختلفة حسب الأولوية
- **اهتزاز ذكي** - أنماط اهتزاز مختلفة للأولويات
- **عرض معلومات المرسل** - صورة، اسم، هاتف، موقع
- **أزرار تفاعلية** - استجابة، تأكيد، عرض الموقع

### 🛡️ لوحة تحكم المسؤولين المتقدمة
- **مراقبة شاملة** - عرض جميع الإنذارات مع تفاصيل المرسلين
- **تتبع المستخدمين** - عرض IP، جهاز، موقع، تاريخ النشاط
- **إدارة الإنذارات** - عرض الرسائل المخصصة وحل الإنذارات
- **إحصائيات مفصلة** - تقارير شاملة عن النشاط
- **اتصال مباشر** - إمكانية الاتصال وإرسال رسائل للمستخدمين
- **تصدير البيانات** - حفظ تقارير مفصلة

### 📱 تطبيق ويب تقدمي (PWA)
- يعمل بدون إنترنت
- قابل للتثبيت على الجهاز
- سرعة تحميل فائقة
- تحديثات تلقائية

## 🚀 التقنيات المستخدمة

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **PWA**: Service Workers, Web App Manifest
- **التخزين**: LocalStorage, IndexedDB
- **الخرائط**: Geolocation API, OpenStreetMap
- **الإشعارات**: Web Notifications API, Push API
- **التصميم**: CSS Grid, Flexbox, Responsive Design

## 📋 متطلبات النظام

### للمستخدمين:
- متصفح حديث يدعم JavaScript
- إذن الوصول للموقع الجغرافي
- إذن الإشعارات (اختياري)

### للمطورين:
- خادم ويب (Apache, Nginx, أو خادم محلي)
- HTTPS (مطلوب للـ PWA والإشعارات)

## 🛠️ التثبيت والإعداد

### 1. تحميل الملفات
```bash
git clone [repository-url]
cd emergency-shield
```

### 2. تشغيل سريع (الطريقة الأسهل)

#### على Windows:
```bash
# انقر مرتين على الملف أو شغل من Command Prompt
start-server.bat
```

#### على Linux/Mac:
```bash
# اجعل الملف قابل للتنفيذ ثم شغله
chmod +x start-server.sh
./start-server.sh
```

#### أو باستخدام Python:
```bash
# شغل السكريبت المخصص
python start-server.py

# أو الطريقة التقليدية
python -m http.server 8000
```

#### أو باستخدام Node.js:
```bash
npx serve .
```

#### أو باستخدام PHP:
```bash
php -S localhost:8000
```

### 3. الوصول للتطبيق
افتح المتصفح وانتقل إلى:
```
http://localhost:8000
```

### 4. لوحة تحكم المسؤولين
للوصول للوحة التحكم:
```
http://localhost:8000/admin.html
```

**بيانات الدخول الافتراضية:**
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

### 5. صفحة المصادقة
للتسجيل أو تسجيل الدخول:
```
http://localhost:8000/auth.html
```

### 6. عرض الإشعارات
لاختبار نظام الإشعارات المتقدم:
```
http://localhost:8000/notifications-demo.html
```

## 🆕 الميزات الجديدة المضافة

### 🔐 نظام المصادقة الكامل
- **تسجيل بالهاتف** - تسجيل آمن برقم الهاتف مع تحقق SMS
- **ملفات مستخدمين** - معلومات كاملة مع تتبع IP والجهاز
- **أنواع حسابات** - مواطن، طوارئ، أمن، طبي، إطفاء، مسؤول
- **جلسات آمنة** - إدارة جلسات المستخدمين لمدة 30 يوم

### 💬 الرسائل المخصصة
- **كتابة حرة** - إضافة رسالة مخصصة مع كل إنذار (500 حرف)
- **قوالب سريعة** - رسائل جاهزة للاستخدام السريع
- **عداد الأحرف** - مراقبة طول الرسالة مع تغيير الألوان
- **عرض في الإشعارات** - إظهار الرسائل في جميع الإشعارات

### 📱 إشعارات SMS حقيقية
- **إرسال تلقائي** - رسائل نصية لجميع المشتركين القريبين
- **نطاق ذكي** - إشعار المستخدمين في نطاق 5 كم
- **خدمات الطوارئ** - إرسال تلقائي للجهات المختصة
- **تفاصيل المرسل** - عرض اسم وهاتف المرسل في الرسائل

### 🎨 واجهة إشعارات متقدمة
- **تصميم جميل** - إشعارات مرئية متفاعلة مع تدرجات لونية
- **أصوات ذكية** - نغمات مختلفة حسب نوع وأولوية الإنذار
- **اهتزاز متدرج** - أنماط اهتزاز مختلفة للأولويات
- **تفاعل كامل** - أزرار استجابة، تأكيد، عرض الموقع
- **معلومات المرسل** - صورة رمزية، اسم، هاتف، منطقة

### 👥 تتبع المستخدمين للمسؤولين
- **معلومات شاملة** - عرض IP، نوع الجهاز، تاريخ التسجيل
- **تاريخ النشاط** - آخر دخول وآخر نشاط
- **إحصائيات الإنذارات** - عدد الإنذارات لكل مستخدم
- **اتصال مباشر** - إمكانية الاتصال وإرسال رسائل
- **إدارة الحسابات** - إيقاف مؤقت وتصدير البيانات

## 📁 هيكل المشروع المحدث

```
emergency-shield/
├── index.html              # الصفحة الرئيسية المحدثة
├── admin.html              # لوحة تحكم المسؤولين المتقدمة
├── auth.html               # صفحة المصادقة والتسجيل ⭐ جديد
├── test.html               # صفحة الاختبار الشاملة
├── notifications-demo.html # عرض نظام الإشعارات ⭐ جديد
├── styles.css              # تنسيقات التطبيق المحدثة
├── admin-styles.css        # تنسيقات لوحة التحكم المحدثة
├── auth-styles.css         # تنسيقات المصادقة ⭐ جديد
├── manifest.json           # ملف PWA
├── sw.js                   # Service Worker
├── js/
│   ├── app.js              # التطبيق الرئيسي المحدث
│   ├── auth.js             # نظام المصادقة ⭐ جديد
│   ├── sms-service.js      # خدمة الرسائل النصية ⭐ جديد
│   ├── enhanced-notifications.js # نظام الإشعارات المتقدم ⭐ جديد
│   ├── location.js         # إدارة المواقع
│   ├── alerts.js           # إدارة الإنذارات
│   ├── notifications.js    # إدارة الإشعارات الأساسية
│   ├── maps.js             # نظام الخرائط
│   ├── websocket.js        # الاتصال المباشر
│   ├── database.js         # قاعدة البيانات
│   └── admin.js            # لوحة التحكم المحدثة
├── icons/                  # مجلد الأيقونات
└── README.md               # دليل المشروع المحدث
```

## 🎯 كيفية الاستخدام

### للمستخدمين الجدد:

1. **التسجيل**: افتح `auth.html` وسجل برقم هاتفك
2. **التحقق**: أدخل رمز التحقق المرسل عبر SMS
3. **الدخول للتطبيق**: انتقل إلى `index.html`
4. **السماح بالموقع**: اقبل طلب الوصول للموقع الجغرافي
5. **إرسال إنذار**: اضغط على نوع الإنذار المناسب
6. **إضافة رسالة**: اكتب رسالة مخصصة (اختيارية)
7. **تأكيد الإرسال**: راجع التفاصيل واضغط "إرسال الإنذار"

### للمستخدمين المسجلين:

1. **تسجيل الدخول**: افتح `auth.html` وادخل رقم هاتفك
2. **التحقق**: أدخل رمز التحقق المرسل
3. **استخدام التطبيق**: ابدأ في إرسال الإنذارات

### للمسؤولين:

1. **تسجيل الدخول**: ادخل بيانات المسؤول
2. **مراقبة الإنذارات**: تابع الإنذارات الواردة
3. **إدارة النظام**: استخدم الإعدادات لتخصيص النظام
4. **تصدير البيانات**: احفظ نسخة احتياطية من البيانات

## ⚙️ الإعدادات والتخصيص

### إعدادات الإشعارات:
- تفعيل/إلغاء الإشعارات التلقائية
- تفعيل/إلغاء الأصوات
- تفعيل/إلغاء الاهتزاز

### إعدادات الموقع:
- نطاق الإنذار (بالمتر)
- دقة تحديد الموقع
- حفظ تاريخ المواقع

### إعدادات الأمان:
- تأكيد الإنذارات قبل الإرسال
- تسجيل جميع الأنشطة
- تشفير البيانات الحساسة

## 🔒 الأمان والخصوصية

- جميع البيانات محفوظة محلياً على الجهاز
- لا يتم إرسال بيانات شخصية للخوادم الخارجية
- تشفير البيانات الحساسة
- إمكانية مسح البيانات في أي وقت

## 🌐 دعم المتصفحات

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 11+
- ✅ Edge 79+
- ✅ Opera 47+

## 📱 دعم الأجهزة

- ✅ Android 5.0+
- ✅ iOS 11+
- ✅ Windows 10+
- ✅ macOS 10.12+
- ✅ Linux (جميع التوزيعات الحديثة)

## 🤝 المساهمة في المشروع

نرحب بمساهماتكم لتطوير المشروع:

1. Fork المشروع
2. إنشاء فرع جديد للميزة
3. إضافة التحسينات
4. إرسال Pull Request

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:

- إنشاء Issue في GitHub
- التواصل عبر البريد الإلكتروني
- مراجعة الوثائق

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## 🙏 شكر وتقدير

- شكر خاص لجميع المساهمين في المشروع
- شكر للمجتمعات مفتوحة المصدر
- شكر لمستخدمي التطبيق وملاحظاتهم القيمة

---

## 🚀 التحديثات الجديدة

### الإصدار 2.0 - نظام شامل وحقيقي

تم تطوير التطبيق ليصبح نظام إنذار حقيقي وفعال مع الميزات التالية:

#### ✅ تم إضافة:
- 🔐 **نظام مصادقة كامل** مع تحقق SMS
- 💬 **رسائل مخصصة** مع كل إنذار (500 حرف)
- 📱 **إرسال SMS حقيقي** لجميع المشتركين القريبين
- 🎨 **واجهة إشعارات متقدمة** مع تفاعل كامل
- 👥 **تتبع شامل للمستخدمين** مع IP والجهاز
- 🔊 **أصوات واهتزاز ذكي** حسب الأولوية
- 📊 **إحصائيات مفصلة** وتقارير شاملة
- 🗺️ **عرض الموقع** على الخرائط مباشرة
- 📞 **اتصال مباشر** بالمستخدمين من لوحة التحكم
- 💾 **تصدير البيانات** والتقارير المفصلة

#### 🔧 تحسينات:
- تصميم محسن ومتجاوب بالكامل
- أداء أفضل وسرعة استجابة عالية
- أمان معزز مع تشفير البيانات
- واجهة مستخدم محسنة وسهلة الاستخدام
- دعم أفضل للأجهزة المختلفة

#### 📋 الصفحات الجديدة:
- `auth.html` - صفحة المصادقة والتسجيل الكاملة
- `notifications-demo.html` - عرض تفاعلي لنظام الإشعارات

#### 🎯 الهدف:
تحويل التطبيق من نموذج أولي إلى **نظام إنذار حقيقي وفعال** يمكن استخدامه في حالات الطوارئ الفعلية مع ضمان وصول الإنذارات لجميع المعنيين.

## 🔧 إصلاح الأخطاء والمشاكل الشائعة

### ❌ مشاكل التشغيل

#### "لا يمكن الوصول للخادم"
```bash
# تأكد من تشغيل الخادم أولاً
./start-server.sh
# أو
start-server.bat
```

#### "الملفات مفقودة"
- تأكد من وجود جميع الملفات في نفس المجلد
- استخدم `final-check.html` للتحقق من الملفات

#### "المنفذ مستخدم"
- الخادم سيجد منفذ متاح تلقائياً
- أو استخدم منفذ مختلف: `python -m http.server 8001`

### 🔐 مشاكل المصادقة

#### "رمز التحقق لا يعمل"
- استخدم أي رقم مكون من 6 أرقام (مثل: 123456)
- تأكد من تفعيل JavaScript في المتصفح

#### "لا يمكن التسجيل"
- استخدم رقم هاتف بالصيغة: +970xxxxxxxxx
- امسح cache المتصفح وحاول مرة أخرى

### 🔔 مشاكل الإشعارات

#### "الإشعارات لا تظهر"
- اسمح للمتصفح بإرسال الإشعارات
- جرب `notifications-demo.html` للاختبار

#### "لا يوجد صوت"
- تأكد من عدم كتم الصوت في المتصفح
- اسمح بتشغيل الصوت التلقائي

### 📍 مشاكل الموقع

#### "لا يمكن تحديد الموقع"
- اسمح للمتصفح بالوصول للموقع الجغرافي
- تأكد من تفعيل GPS في الجهاز

### 🛡️ مشاكل لوحة التحكم

#### "لا يمكن الدخول للوحة التحكم"
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

#### "لوحة التحكم فارغة"
- أرسل بعض الإنذارات التجريبية أولاً
- تأكد من تحميل جميع الملفات

### 🧪 أدوات الاختبار والإصلاح

#### للاختبار السريع:
```
http://localhost:8000/quick-test.html
```

#### للفحص الشامل:
```
http://localhost:8000/final-check.html
```

#### لعرض الإشعارات:
```
http://localhost:8000/notifications-demo.html
```

### 🔧 إصلاحات تلقائية

التطبيق يحتوي على نظام إصلاح تلقائي في `fix-errors.js` يتعامل مع:
- الأخطاء البرمجية الشائعة
- مشاكل التوافق
- الدوال المفقودة
- إعدادات المتصفح

**درع الطوارئ - نظام إنذار حقيقي وفعال للجميع** 🛡️
