<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض الإشعارات - درع الطوارئ</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 2rem;
            min-height: 100vh;
        }

        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .demo-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .demo-header h1 {
            color: #1e3a8a;
            margin-bottom: 0.5rem;
        }

        .demo-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: #f8fafc;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
        }

        .demo-section h3 {
            color: #1e3a8a;
            margin-top: 0;
        }

        .demo-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .demo-btn {
            background: #dc2626;
            color: white;
            border: none;
            padding: 1rem;
            border-radius: 12px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .demo-btn:hover {
            background: #b91c1c;
            transform: translateY(-2px);
        }

        .demo-btn.secondary {
            background: #3b82f6;
        }

        .demo-btn.secondary:hover {
            background: #2563eb;
        }

        .demo-btn.warning {
            background: #f59e0b;
        }

        .demo-btn.warning:hover {
            background: #d97706;
        }

        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .setting-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.75rem;
            background: white;
            border-radius: 8px;
            border: 1px solid #d1d5db;
        }

        .setting-label {
            font-weight: 500;
            color: #374151;
        }

        .toggle-switch {
            position: relative;
            width: 50px;
            height: 24px;
            background: #d1d5db;
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.2s;
        }

        .toggle-switch.active {
            background: #10b981;
        }

        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.2s;
        }

        .toggle-switch.active::after {
            transform: translateX(26px);
        }

        .info-box {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
        }

        .info-box h4 {
            color: #1e40af;
            margin: 0 0 0.5rem 0;
        }

        .info-box p {
            color: #1e40af;
            margin: 0;
            font-size: 0.875rem;
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .status-indicator.success {
            background: #f0fdf4;
            color: #16a34a;
            border: 1px solid #bbf7d0;
        }

        .status-indicator.error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }

        .status-indicator.warning {
            background: #fffbeb;
            color: #f59e0b;
            border: 1px solid #fed7aa;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🛡️ عرض نظام الإشعارات المتقدم</h1>
            <p>اختبر جميع أنواع الإشعارات والتنبيهات</p>
            <div id="notification-status" class="status-indicator">
                <span>🔄</span>
                جاري التحقق من الأذونات...
            </div>
        </div>

        <div class="demo-section">
            <h3>إشعارات الطوارئ</h3>
            <p>اختبر الإشعارات المختلفة مع الأصوات والاهتزاز</p>
            <div class="demo-buttons">
                <button class="demo-btn" onclick="testEmergencyNotification('ambulance', 'critical')">
                    <span>🆘</span>
                    إسعاف عاجل
                </button>
                <button class="demo-btn" onclick="testEmergencyNotification('fire', 'critical')">
                    <span>🔥</span>
                    حريق
                </button>
                <button class="demo-btn" onclick="testEmergencyNotification('settler-attack', 'critical')">
                    <span>⚠️</span>
                    هجوم مستوطنين
                </button>
                <button class="demo-btn" onclick="testEmergencyNotification('civil-defense', 'high')">
                    <span>🛡️</span>
                    دفاع مدني
                </button>
                <button class="demo-btn warning" onclick="testEmergencyNotification('settlers-present', 'high')">
                    <span>🚷</span>
                    مستوطنين في الموقع
                </button>
                <button class="demo-btn secondary" onclick="testEmergencyNotification('intrusion', 'critical')">
                    <span>🚨</span>
                    اقتحام
                </button>
            </div>
        </div>

        <div class="demo-section">
            <h3>إشعارات مخصصة</h3>
            <p>اختبر الإشعارات مع رسائل مخصصة</p>
            <div class="demo-buttons">
                <button class="demo-btn" onclick="testCustomNotification()">
                    <span>💬</span>
                    إشعار مع رسالة مخصصة
                </button>
                <button class="demo-btn secondary" onclick="testMultipleNotifications()">
                    <span>📢</span>
                    إشعارات متعددة
                </button>
                <button class="demo-btn warning" onclick="testLongMessage()">
                    <span>📝</span>
                    رسالة طويلة
                </button>
            </div>
        </div>

        <div class="demo-section">
            <h3>إعدادات الإشعارات</h3>
            <div class="settings-grid">
                <div class="setting-item">
                    <span class="setting-label">الأصوات</span>
                    <div class="toggle-switch active" id="sound-toggle" onclick="toggleSound()"></div>
                </div>
                <div class="setting-item">
                    <span class="setting-label">الاهتزاز</span>
                    <div class="toggle-switch active" id="vibration-toggle" onclick="toggleVibration()"></div>
                </div>
                <div class="setting-item">
                    <span class="setting-label">الإشعارات المتقدمة</span>
                    <div class="toggle-switch active" id="enhanced-toggle" onclick="toggleEnhanced()"></div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>إجراءات الإدارة</h3>
            <div class="demo-buttons">
                <button class="demo-btn secondary" onclick="clearAllNotifications()">
                    <span>🗑️</span>
                    مسح جميع الإشعارات
                </button>
                <button class="demo-btn secondary" onclick="testNotificationPermission()">
                    <span>🔐</span>
                    طلب أذونات الإشعارات
                </button>
                <button class="demo-btn warning" onclick="testSoundOnly()">
                    <span>🔊</span>
                    اختبار الصوت فقط
                </button>
            </div>
        </div>

        <div class="info-box">
            <h4>معلومات مهمة</h4>
            <p>• الإشعارات الحرجة لا تختفي تلقائياً وتتطلب تفاعل المستخدم</p>
            <p>• يتم عرض معلومات المرسل كاملة للمسؤولين فقط</p>
            <p>• الأصوات والاهتزاز يختلفان حسب أولوية الإنذار</p>
            <p>• يمكن عرض الموقع على الخريطة مباشرة من الإشعار</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/enhanced-notifications.js"></script>
    <script>
        // Demo functions
        function testEmergencyNotification(type, priority) {
            const alertData = {
                id: 'demo_' + Date.now(),
                type: type,
                priority: priority,
                customMessage: getRandomMessage(type),
                location: {
                    latitude: 31.7683 + (Math.random() - 0.5) * 0.01,
                    longitude: 35.2137 + (Math.random() - 0.5) * 0.01,
                    accuracy: Math.floor(Math.random() * 50) + 10
                },
                timestamp: new Date().toISOString(),
                senderInfo: {
                    fullName: getRandomName(),
                    phoneNumber: '+970' + Math.floor(Math.random() * 900000000 + 100000000),
                    userRole: getRandomRole(),
                    locationArea: getRandomArea(),
                    ipAddress: getRandomIP(),
                    deviceInfo: getRandomDevice()
                }
            };

            if (window.enhancedNotificationManager) {
                window.enhancedNotificationManager.showEmergencyNotification(alertData);
            }
        }

        function testCustomNotification() {
            const message = prompt('أدخل رسالة مخصصة:') || 'رسالة تجريبية مخصصة';
            testEmergencyNotification('ambulance', 'high');
        }

        function testMultipleNotifications() {
            const types = ['ambulance', 'fire', 'civil-defense'];
            const priorities = ['critical', 'high', 'medium'];
            
            types.forEach((type, index) => {
                setTimeout(() => {
                    testEmergencyNotification(type, priorities[index]);
                }, index * 2000);
            });
        }

        function testLongMessage() {
            const longMessage = 'هذه رسالة طويلة جداً لاختبار كيفية عرض النص الطويل في الإشعارات. يجب أن يتم عرضها بشكل صحيح ومقروء. هذا النص يحتوي على تفاصيل كثيرة حول الحالة الطارئة والإجراءات المطلوبة.';
            
            const alertData = {
                id: 'demo_long_' + Date.now(),
                type: 'civil-defense',
                priority: 'high',
                customMessage: longMessage,
                location: { latitude: 31.7683, longitude: 35.2137 },
                timestamp: new Date().toISOString(),
                senderInfo: {
                    fullName: 'محمد أحمد الطويل',
                    phoneNumber: '+970591234567',
                    userRole: 'citizen',
                    locationArea: 'القدس الشريف',
                    ipAddress: '*************',
                    deviceInfo: 'Android'
                }
            };

            if (window.enhancedNotificationManager) {
                window.enhancedNotificationManager.showEmergencyNotification(alertData);
            }
        }

        function clearAllNotifications() {
            if (window.enhancedNotificationManager) {
                window.enhancedNotificationManager.clearAllNotifications();
            }
        }

        function testNotificationPermission() {
            if ('Notification' in window) {
                Notification.requestPermission().then(permission => {
                    updateNotificationStatus();
                    if (permission === 'granted') {
                        alert('تم منح أذونات الإشعارات بنجاح!');
                    } else {
                        alert('لم يتم منح أذونات الإشعارات');
                    }
                });
            }
        }

        function testSoundOnly() {
            if (window.enhancedNotificationManager) {
                window.enhancedNotificationManager.playAlertSound('critical');
                window.enhancedNotificationManager.vibrateDevice('critical');
            }
        }

        function toggleSound() {
            const toggle = document.getElementById('sound-toggle');
            toggle.classList.toggle('active');
            
            if (window.enhancedNotificationManager) {
                const enabled = window.enhancedNotificationManager.toggleSound();
                if (!enabled) {
                    toggle.classList.remove('active');
                }
            }
        }

        function toggleVibration() {
            const toggle = document.getElementById('vibration-toggle');
            toggle.classList.toggle('active');
        }

        function toggleEnhanced() {
            const toggle = document.getElementById('enhanced-toggle');
            toggle.classList.toggle('active');
        }

        function updateNotificationStatus() {
            const statusElement = document.getElementById('notification-status');
            
            if (!('Notification' in window)) {
                statusElement.className = 'status-indicator error';
                statusElement.innerHTML = '<span>❌</span> الإشعارات غير مدعومة';
                return;
            }

            const permission = Notification.permission;
            switch (permission) {
                case 'granted':
                    statusElement.className = 'status-indicator success';
                    statusElement.innerHTML = '<span>✅</span> الإشعارات مفعلة';
                    break;
                case 'denied':
                    statusElement.className = 'status-indicator error';
                    statusElement.innerHTML = '<span>❌</span> الإشعارات مرفوضة';
                    break;
                default:
                    statusElement.className = 'status-indicator warning';
                    statusElement.innerHTML = '<span>⚠️</span> أذونات الإشعارات مطلوبة';
                    break;
            }
        }

        // Helper functions for demo data
        function getRandomMessage(type) {
            const messages = {
                'ambulance': ['حالة طبية طارئة', 'إصابة خطيرة', 'نوبة قلبية', 'حادث سير'],
                'fire': ['حريق في المبنى', 'دخان كثيف', 'انفجار', 'حريق غابات'],
                'civil-defense': ['انهيار مبنى', 'فيضانات', 'انقطاع كهرباء', 'حالة طوارئ عامة'],
                'settler-attack': ['هجوم مسلح', 'اعتداء على المواطنين', 'تدمير ممتلكات', 'تهديد بالسلاح'],
                'settlers-present': ['مستوطنين مسلحين', 'تجمع مشبوه', 'محاولة اقتحام', 'استفزازات'],
                'intrusion': ['اقتحام منزل', 'دخول غير مشروع', 'سرقة', 'تهديد']
            };
            
            const typeMessages = messages[type] || ['حالة طوارئ'];
            return typeMessages[Math.floor(Math.random() * typeMessages.length)];
        }

        function getRandomName() {
            const names = ['أحمد محمد', 'فاطمة علي', 'محمود حسن', 'عائشة أحمد', 'يوسف إبراهيم', 'مريم خالد'];
            return names[Math.floor(Math.random() * names.length)];
        }

        function getRandomRole() {
            const roles = ['citizen', 'emergency', 'security', 'medical'];
            return roles[Math.floor(Math.random() * roles.length)];
        }

        function getRandomArea() {
            const areas = ['القدس', 'رام الله', 'بيت لحم', 'الخليل', 'نابلس', 'جنين'];
            return areas[Math.floor(Math.random() * areas.length)];
        }

        function getRandomIP() {
            return `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`;
        }

        function getRandomDevice() {
            const devices = ['Android', 'iOS', 'Windows', 'Mac'];
            return devices[Math.floor(Math.random() * devices.length)];
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            updateNotificationStatus();
            
            // Auto-test notification after 3 seconds
            setTimeout(() => {
                if (Notification.permission === 'granted') {
                    testEmergencyNotification('ambulance', 'critical');
                }
            }, 3000);
        });
    </script>
</body>
</html>
