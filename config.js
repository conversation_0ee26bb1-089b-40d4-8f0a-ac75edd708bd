// Emergency Shield Configuration
window.EmergencyShieldConfig = {
    // Application Settings
    app: {
        name: 'درع الطوارئ',
        version: '2.0.0',
        language: 'ar',
        direction: 'rtl',
        debug: false
    },

    // SMS Service Configuration
    sms: {
        enabled: true,
        provider: 'local', // 'local', 'twilio', 'nexmo'
        defaultCountryCode: '+970',
        verificationCodeLength: 6,
        verificationCodeExpiry: 300000, // 5 minutes
        maxRetries: 3,
        rateLimit: {
            maxPerHour: 10,
            maxPerDay: 50
        }
    },

    // Authentication Settings
    auth: {
        sessionDuration: 30 * 24 * 60 * 60 * 1000, // 30 days
        requirePhoneVerification: true,
        allowGuestMode: false,
        maxLoginAttempts: 5,
        lockoutDuration: 15 * 60 * 1000 // 15 minutes
    },

    // Alert System Configuration
    alerts: {
        maxCustomMessageLength: 500,
        defaultRadius: 5000, // 5km
        maxRadius: 50000, // 50km
        priorities: {
            critical: {
                color: '#dc2626',
                sound: 'critical-alert.mp3',
                vibration: [200, 100, 200, 100, 200],
                autoExpire: false
            },
            high: {
                color: '#f59e0b',
                sound: 'high-alert.mp3',
                vibration: [200, 100, 200],
                autoExpire: 300000 // 5 minutes
            },
            medium: {
                color: '#3b82f6',
                sound: 'medium-alert.mp3',
                vibration: [200],
                autoExpire: 180000 // 3 minutes
            },
            low: {
                color: '#10b981',
                sound: 'low-alert.mp3',
                vibration: [100],
                autoExpire: 120000 // 2 minutes
            }
        },
        types: {
            'ambulance': {
                title: 'إسعاف',
                icon: '🆘',
                priority: 'critical',
                emergencyContacts: ['+970-emergency-ambulance']
            },
            'fire': {
                title: 'حريق',
                icon: '🔥',
                priority: 'critical',
                emergencyContacts: ['+970-emergency-fire']
            },
            'civil-defense': {
                title: 'دفاع مدني',
                icon: '🛡️',
                priority: 'high',
                emergencyContacts: ['+970-emergency-civil']
            },
            'settler-attack': {
                title: 'هجوم مستوطنين',
                icon: '⚠️',
                priority: 'critical',
                emergencyContacts: ['+970-emergency-security']
            },
            'settlers-present': {
                title: 'مستوطنين في الموقع',
                icon: '🚷',
                priority: 'high',
                emergencyContacts: ['+970-emergency-security']
            },
            'intrusion': {
                title: 'اقتحام',
                icon: '🚨',
                priority: 'critical',
                emergencyContacts: ['+970-emergency-police']
            }
        }
    },

    // Notification Settings
    notifications: {
        enabled: true,
        sound: true,
        vibration: true,
        showSenderInfo: true,
        persistCritical: true,
        maxVisible: 5,
        defaultDuration: 10000, // 10 seconds
        position: 'top-right'
    },

    // Location Settings
    location: {
        enableGPS: true,
        accuracy: 'high', // 'high', 'medium', 'low'
        timeout: 10000, // 10 seconds
        maximumAge: 60000, // 1 minute
        fallbackToIP: true,
        saveHistory: true,
        maxHistoryEntries: 100
    },

    // Database Configuration
    database: {
        name: 'EmergencyShieldDB',
        version: 1,
        stores: {
            alerts: 'id, timestamp, type, status',
            users: 'userId, phoneNumber, registrationDate',
            sessions: 'sessionId, userId, createdAt',
            locations: 'id, userId, timestamp'
        },
        maxEntries: {
            alerts: 1000,
            users: 10000,
            sessions: 100,
            locations: 500
        }
    },

    // Admin Panel Settings
    admin: {
        defaultCredentials: {
            username: 'admin',
            password: 'admin123'
        },
        sessionTimeout: 60 * 60 * 1000, // 1 hour
        maxLoginAttempts: 3,
        features: {
            userTracking: true,
            ipLogging: true,
            deviceFingerprinting: true,
            exportData: true,
            sendMessages: true,
            suspendUsers: true
        }
    },

    // Security Settings
    security: {
        enableCSP: true,
        enableXSSProtection: true,
        enableClickjacking: true,
        encryptSensitiveData: true,
        logSecurityEvents: true,
        maxFailedAttempts: 5,
        bruteForceProtection: true
    },

    // Performance Settings
    performance: {
        enableServiceWorker: true,
        enableCaching: true,
        cacheStrategy: 'cache-first',
        maxCacheSize: 50 * 1024 * 1024, // 50MB
        enableCompression: true,
        lazyLoadImages: true
    },

    // UI/UX Settings
    ui: {
        theme: 'default',
        animations: true,
        transitions: true,
        responsiveBreakpoints: {
            mobile: 768,
            tablet: 1024,
            desktop: 1200
        },
        accessibility: {
            highContrast: false,
            largeText: false,
            reduceMotion: false
        }
    },

    // Development Settings
    development: {
        enableDebugMode: false,
        enableConsoleLogging: true,
        enablePerformanceMonitoring: false,
        mockSMSService: true,
        mockLocationService: false,
        enableTestData: false
    },

    // Emergency Contacts
    emergencyContacts: {
        ambulance: ['+970-emergency-ambulance', '+970-backup-ambulance'],
        fire: ['+970-emergency-fire', '+970-backup-fire'],
        police: ['+970-emergency-police', '+970-backup-police'],
        civilDefense: ['+970-emergency-civil', '+970-backup-civil'],
        security: ['+970-emergency-security', '+970-backup-security']
    },

    // API Endpoints (for future use)
    api: {
        baseUrl: 'https://api.emergency-shield.local',
        endpoints: {
            alerts: '/api/v1/alerts',
            users: '/api/v1/users',
            auth: '/api/v1/auth',
            sms: '/api/v1/sms',
            locations: '/api/v1/locations'
        },
        timeout: 30000, // 30 seconds
        retries: 3
    },

    // Feature Flags
    features: {
        enableSMSNotifications: true,
        enablePushNotifications: true,
        enableLocationTracking: true,
        enableUserProfiles: true,
        enableAdminPanel: true,
        enableDataExport: true,
        enableOfflineMode: true,
        enableRealTimeUpdates: true
    }
};

// Apply configuration
(function() {
    'use strict';
    
    const config = window.EmergencyShieldConfig;
    
    // Set debug mode
    if (config.development.enableDebugMode) {
        window.DEBUG = true;
        console.log('Emergency Shield Debug Mode Enabled');
    }
    
    // Set language and direction
    document.documentElement.lang = config.app.language;
    document.documentElement.dir = config.app.direction;
    
    // Apply theme
    if (config.ui.theme !== 'default') {
        document.body.classList.add(`theme-${config.ui.theme}`);
    }
    
    // Disable animations if requested
    if (!config.ui.animations) {
        document.body.classList.add('no-animations');
    }
    
    // Apply accessibility settings
    if (config.ui.accessibility.highContrast) {
        document.body.classList.add('high-contrast');
    }
    
    if (config.ui.accessibility.largeText) {
        document.body.classList.add('large-text');
    }
    
    if (config.ui.accessibility.reduceMotion) {
        document.body.classList.add('reduce-motion');
    }
    
    console.log(`Emergency Shield v${config.app.version} Configuration Loaded`);
})();
