// SMS Service for Emergency Shield
class SMSService {
    constructor() {
        this.apiKey = null; // Will be set from environment or config
        this.provider = 'twilio'; // Default provider
        this.rateLimits = new Map();
        this.maxAttemptsPerHour = 5;
        
        this.init();
    }

    init() {
        // Load configuration
        this.loadConfig();
        
        // Setup rate limiting cleanup
        setInterval(() => {
            this.cleanupRateLimits();
        }, 60000); // Clean every minute
    }

    loadConfig() {
        // In a real application, these would come from environment variables
        // For demo purposes, we'll use placeholder values
        this.config = {
            twilio: {
                accountSid: 'demo_account_sid',
                authToken: 'demo_auth_token',
                fromNumber: '+**********'
            },
            messagebird: {
                apiKey: 'demo_api_key',
                originator: 'EmergencyShield'
            },
            local: {
                enabled: true // For development/testing
            }
        };
    }

    async sendSMS(phoneNumber, message, options = {}) {
        try {
            // Check rate limits
            if (!this.checkRateLimit(phoneNumber)) {
                throw new Error('تم تجاوز الحد المسموح لإرسال الرسائل. يرجى المحاولة لاحقاً.');
            }

            // Validate phone number
            if (!this.validatePhoneNumber(phoneNumber)) {
                throw new Error('رقم الهاتف غير صحيح');
            }

            // Format message
            const formattedMessage = this.formatMessage(message, options);

            // Send based on environment
            let result;
            if (this.isLocalEnvironment()) {
                result = await this.sendLocalSMS(phoneNumber, formattedMessage);
            } else {
                result = await this.sendRealSMS(phoneNumber, formattedMessage, options);
            }

            // Update rate limits
            this.updateRateLimit(phoneNumber);

            // Log the SMS
            await this.logSMS(phoneNumber, formattedMessage, result);

            return result;

        } catch (error) {
            console.error('SMS sending failed:', error);
            await this.logSMSError(phoneNumber, message, error);
            throw error;
        }
    }

    async sendVerificationCode(phoneNumber, code) {
        const message = `رمز التحقق الخاص بك في درع الطوارئ هو: ${code}\n\nلا تشارك هذا الرمز مع أي شخص.\n\nصالح لمدة 10 دقائق.`;
        
        return await this.sendSMS(phoneNumber, message, {
            type: 'verification',
            code: code,
            expiresIn: 600 // 10 minutes
        });
    }

    async sendEmergencyAlert(phoneNumber, alertData) {
        const alertTypes = {
            'ambulance': 'إسعاف',
            'fire': 'حريق',
            'civil-defense': 'دفاع مدني',
            'settler-attack': 'هجوم مستوطنين',
            'settlers-present': 'مستوطنين في الموقع',
            'intrusion': 'اقتحام'
        };

        const alertType = alertTypes[alertData.type] || 'إنذار';
        const location = alertData.location ? 
            `الموقع: ${alertData.location.latitude.toFixed(4)}, ${alertData.location.longitude.toFixed(4)}` : 
            'الموقع: غير محدد';

        let message = `🚨 إنذار طوارئ - ${alertType}\n\n`;
        message += `المرسل: ${alertData.senderName}\n`;
        message += `الهاتف: ${alertData.senderPhone}\n`;
        message += `${location}\n`;
        message += `الوقت: ${new Date(alertData.timestamp).toLocaleString('ar-SA')}\n`;
        
        if (alertData.customMessage) {
            message += `\nالرسالة: ${alertData.customMessage}\n`;
        }
        
        message += `\nدرع الطوارئ`;

        return await this.sendSMS(phoneNumber, message, {
            type: 'emergency',
            priority: 'high',
            alertData: alertData
        });
    }

    async sendRealSMS(phoneNumber, message, options) {
        switch (this.provider) {
            case 'twilio':
                return await this.sendTwilioSMS(phoneNumber, message, options);
            case 'messagebird':
                return await this.sendMessageBirdSMS(phoneNumber, message, options);
            default:
                throw new Error('SMS provider not configured');
        }
    }

    async sendTwilioSMS(phoneNumber, message, options) {
        const url = `https://api.twilio.com/2010-04-01/Accounts/${this.config.twilio.accountSid}/Messages.json`;
        
        const formData = new FormData();
        formData.append('To', phoneNumber);
        formData.append('From', this.config.twilio.fromNumber);
        formData.append('Body', message);

        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Authorization': 'Basic ' + btoa(`${this.config.twilio.accountSid}:${this.config.twilio.authToken}`)
            },
            body: formData
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(`Twilio error: ${error.message}`);
        }

        const result = await response.json();
        return {
            success: true,
            messageId: result.sid,
            provider: 'twilio',
            status: result.status
        };
    }

    async sendMessageBirdSMS(phoneNumber, message, options) {
        const url = 'https://rest.messagebird.com/messages';
        
        const payload = {
            originator: this.config.messagebird.originator,
            recipients: [phoneNumber],
            body: message
        };

        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Authorization': `AccessKey ${this.config.messagebird.apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(`MessageBird error: ${error.errors?.[0]?.description || 'Unknown error'}`);
        }

        const result = await response.json();
        return {
            success: true,
            messageId: result.id,
            provider: 'messagebird',
            status: 'sent'
        };
    }

    async sendLocalSMS(phoneNumber, message) {
        // For local development - simulate SMS sending
        console.log('=== LOCAL SMS SIMULATION ===');
        console.log(`To: ${phoneNumber}`);
        console.log(`Message: ${message}`);
        console.log('============================');

        // Show alert in development
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            // Extract verification code if present
            const codeMatch = message.match(/(\d{6})/);
            if (codeMatch) {
                setTimeout(() => {
                    alert(`رمز التحقق للاختبار: ${codeMatch[1]}`);
                }, 500);
            }
        }

        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        return {
            success: true,
            messageId: 'local_' + Date.now(),
            provider: 'local',
            status: 'delivered'
        };
    }

    validatePhoneNumber(phoneNumber) {
        // Remove any non-digit characters except +
        const cleaned = phoneNumber.replace(/[^\d+]/g, '');
        
        // Check if it starts with + and has 10-15 digits
        const phoneRegex = /^\+\d{10,15}$/;
        return phoneRegex.test(cleaned);
    }

    formatMessage(message, options = {}) {
        let formatted = message;

        // Add timestamp if not emergency (emergency messages have their own timestamp)
        if (options.type !== 'emergency') {
            formatted += `\n\nتم الإرسال: ${new Date().toLocaleString('ar-SA')}`;
        }

        // Ensure message doesn't exceed SMS limits (160 characters for single SMS)
        if (formatted.length > 1600) { // Allow up to 10 SMS parts
            formatted = formatted.substring(0, 1597) + '...';
        }

        return formatted;
    }

    checkRateLimit(phoneNumber) {
        const now = Date.now();
        const hourAgo = now - (60 * 60 * 1000);
        
        if (!this.rateLimits.has(phoneNumber)) {
            this.rateLimits.set(phoneNumber, []);
        }

        const attempts = this.rateLimits.get(phoneNumber);
        
        // Remove old attempts
        const recentAttempts = attempts.filter(timestamp => timestamp > hourAgo);
        this.rateLimits.set(phoneNumber, recentAttempts);

        return recentAttempts.length < this.maxAttemptsPerHour;
    }

    updateRateLimit(phoneNumber) {
        const now = Date.now();
        
        if (!this.rateLimits.has(phoneNumber)) {
            this.rateLimits.set(phoneNumber, []);
        }

        const attempts = this.rateLimits.get(phoneNumber);
        attempts.push(now);
        this.rateLimits.set(phoneNumber, attempts);
    }

    cleanupRateLimits() {
        const hourAgo = Date.now() - (60 * 60 * 1000);
        
        for (const [phoneNumber, attempts] of this.rateLimits.entries()) {
            const recentAttempts = attempts.filter(timestamp => timestamp > hourAgo);
            
            if (recentAttempts.length === 0) {
                this.rateLimits.delete(phoneNumber);
            } else {
                this.rateLimits.set(phoneNumber, recentAttempts);
            }
        }
    }

    async logSMS(phoneNumber, message, result) {
        const logEntry = {
            id: Date.now().toString(),
            phoneNumber: this.maskPhoneNumber(phoneNumber),
            messageLength: message.length,
            provider: result.provider,
            messageId: result.messageId,
            status: result.status,
            timestamp: new Date().toISOString(),
            success: result.success
        };

        // Store in localStorage for demo (in production, this would go to a proper logging service)
        const logs = JSON.parse(localStorage.getItem('smsLogs') || '[]');
        logs.unshift(logEntry);
        
        // Keep only last 1000 logs
        if (logs.length > 1000) {
            logs.splice(1000);
        }
        
        localStorage.setItem('smsLogs', JSON.stringify(logs));
    }

    async logSMSError(phoneNumber, message, error) {
        const errorLog = {
            id: Date.now().toString(),
            phoneNumber: this.maskPhoneNumber(phoneNumber),
            messageLength: message.length,
            error: error.message,
            timestamp: new Date().toISOString(),
            success: false
        };

        const errorLogs = JSON.parse(localStorage.getItem('smsErrorLogs') || '[]');
        errorLogs.unshift(errorLog);
        
        // Keep only last 500 error logs
        if (errorLogs.length > 500) {
            errorLogs.splice(500);
        }
        
        localStorage.setItem('smsErrorLogs', JSON.stringify(errorLogs));
    }

    maskPhoneNumber(phoneNumber) {
        // Mask phone number for privacy (show only last 4 digits)
        if (phoneNumber.length > 4) {
            return phoneNumber.substring(0, phoneNumber.length - 4).replace(/\d/g, '*') + 
                   phoneNumber.substring(phoneNumber.length - 4);
        }
        return phoneNumber;
    }

    isLocalEnvironment() {
        return window.location.hostname === 'localhost' || 
               window.location.hostname === '127.0.0.1' ||
               window.location.protocol === 'file:' ||
               this.config.local.enabled;
    }

    // Admin functions
    getSMSStats() {
        const logs = JSON.parse(localStorage.getItem('smsLogs') || '[]');
        const errorLogs = JSON.parse(localStorage.getItem('smsErrorLogs') || '[]');
        
        const today = new Date().toDateString();
        const todayLogs = logs.filter(log => new Date(log.timestamp).toDateString() === today);
        const todayErrors = errorLogs.filter(log => new Date(log.timestamp).toDateString() === today);

        return {
            total: logs.length,
            todayTotal: todayLogs.length,
            todaySuccess: todayLogs.filter(log => log.success).length,
            todayErrors: todayErrors.length,
            successRate: logs.length > 0 ? (logs.filter(log => log.success).length / logs.length * 100).toFixed(2) : 0
        };
    }

    getRecentSMSLogs(limit = 50) {
        const logs = JSON.parse(localStorage.getItem('smsLogs') || '[]');
        return logs.slice(0, limit);
    }

    clearSMSLogs() {
        localStorage.removeItem('smsLogs');
        localStorage.removeItem('smsErrorLogs');
    }

    // Test function
    async testSMS(phoneNumber) {
        const testMessage = 'هذه رسالة اختبار من درع الطوارئ. النظام يعمل بشكل صحيح.';
        return await this.sendSMS(phoneNumber, testMessage, { type: 'test' });
    }
}

// Create global instance
window.smsService = new SMSService();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SMSService;
}
