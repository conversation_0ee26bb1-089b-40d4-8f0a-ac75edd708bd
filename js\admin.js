// Admin Dashboard Controller
class AdminDashboard {
    constructor() {
        this.isAuthenticated = false;
        this.currentSection = 'dashboard';
        this.refreshInterval = null;
        this.settings = {
            autoNotifications: true,
            soundAlerts: true,
            alertRadius: 5000,
            requireConfirmation: true,
            logActivities: true,
            refreshInterval: 30,
            maxAlerts: 1000
        };
        
        this.init();
    }

    async init() {
        // Check authentication
        this.checkAuthentication();
        
        if (!this.isAuthenticated) {
            this.showLoginScreen();
            this.setupLoginHandlers();
        } else {
            this.showDashboard();
            this.setupDashboardHandlers();
            this.loadDashboardData();
            this.startAutoRefresh();
        }
    }

    checkAuthentication() {
        const token = localStorage.getItem('adminToken');
        const expiry = localStorage.getItem('adminTokenExpiry');
        
        if (token && expiry && new Date().getTime() < parseInt(expiry)) {
            this.isAuthenticated = true;
        }
    }

    showLoginScreen() {
        document.getElementById('login-screen').classList.remove('hidden');
        document.getElementById('admin-dashboard').classList.add('hidden');
    }

    showDashboard() {
        document.getElementById('login-screen').classList.add('hidden');
        document.getElementById('admin-dashboard').classList.remove('hidden');
    }

    setupLoginHandlers() {
        const loginForm = document.getElementById('login-form');
        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            await this.handleLogin(e);
        });
    }

    async handleLogin(e) {
        const formData = new FormData(e.target);
        const username = formData.get('username');
        const password = formData.get('password');
        const errorDiv = document.getElementById('login-error');

        try {
            // Simulate authentication - replace with real API call
            if (username === 'admin' && password === 'JaMaL@123') {
                // Set authentication
                const token = 'admin_' + Date.now();
                const expiry = new Date().getTime() + (24 * 60 * 60 * 1000); // 24 hours
                
                localStorage.setItem('adminToken', token);
                localStorage.setItem('adminTokenExpiry', expiry.toString());
                localStorage.setItem('isAdmin', 'true');
                
                this.isAuthenticated = true;
                this.showDashboard();
                this.setupDashboardHandlers();
                this.loadDashboardData();
                this.startAutoRefresh();
            } else {
                throw new Error('اسم المستخدم أو كلمة المرور غير صحيحة');
            }
        } catch (error) {
            errorDiv.textContent = error.message;
            errorDiv.classList.remove('hidden');
        }
    }

    setupDashboardHandlers() {
        // Navigation
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const section = e.currentTarget.dataset.section;
                this.switchSection(section);
            });
        });

        // Logout
        document.getElementById('logout-btn').addEventListener('click', () => {
            this.logout();
        });

        // Refresh data
        document.getElementById('refresh-data').addEventListener('click', () => {
            this.loadDashboardData();
        });

        // Modal handlers
        this.setupModalHandlers();

        // Settings handlers
        this.setupSettingsHandlers();

        // Filter handlers
        this.setupFilterHandlers();
    }

    setupModalHandlers() {
        // Close modal buttons
        document.querySelectorAll('.close-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const modal = e.target.closest('.modal');
                this.hideModal(modal.id);
            });
        });

        // Modal backdrop clicks
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.hideModal(modal.id);
                }
            });
        });
    }

    setupSettingsHandlers() {
        // Save settings
        document.getElementById('save-settings').addEventListener('click', () => {
            this.saveSettings();
        });

        // Reset settings
        document.getElementById('reset-settings').addEventListener('click', () => {
            this.resetSettings();
        });

        // Export data
        document.getElementById('export-data').addEventListener('click', () => {
            this.exportData();
        });

        // Clear old data
        document.getElementById('clear-old-data').addEventListener('click', () => {
            this.clearOldData();
        });
    }

    setupFilterHandlers() {
        // Alert filters
        document.getElementById('alert-filter')?.addEventListener('change', () => {
            this.filterAlerts();
        });

        document.getElementById('alert-type-filter')?.addEventListener('change', () => {
            this.filterAlerts();
        });
    }

    switchSection(sectionName) {
        // Update navigation
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');

        // Update sections
        document.querySelectorAll('.admin-section').forEach(section => {
            section.classList.remove('active');
        });
        document.getElementById(`${sectionName}-section`).classList.add('active');

        this.currentSection = sectionName;

        // Load section-specific data
        this.loadSectionData(sectionName);
    }

    async loadSectionData(sectionName) {
        switch (sectionName) {
            case 'dashboard':
                this.loadDashboardData();
                break;
            case 'alerts':
                this.loadAlertsData();
                break;
            case 'users':
                this.loadUsersData();
                break;
            case 'map':
                this.loadMapData();
                break;
            case 'settings':
                this.loadSettingsData();
                break;
        }
    }

    async loadDashboardData() {
        try {
            // Update last update time
            document.getElementById('last-update-time').textContent = 
                new Date().toLocaleString('ar-SA');

            // Load statistics
            await this.loadStatistics();
            
            // Load recent alerts
            await this.loadRecentAlerts();
            
        } catch (error) {
            console.error('Failed to load dashboard data:', error);
        }
    }

    async loadStatistics() {
        // Get statistics from alert manager
        const stats = window.alertManager ? window.alertManager.getStatistics() : {
            total: 0,
            active: 0,
            today: 0,
            thisWeek: 0,
            thisMonth: 0
        };

        // Update stat cards
        document.getElementById('active-alerts').textContent = stats.active || 0;
        document.getElementById('today-alerts').textContent = stats.today || 0;
        document.getElementById('online-users').textContent = this.getOnlineUsersCount();
        document.getElementById('resolved-alerts').textContent = 
            (stats.total || 0) - (stats.active || 0);
    }

    async loadRecentAlerts() {
        const alertsList = document.getElementById('recent-alerts-list');
        
        if (!window.alertManager) {
            alertsList.innerHTML = '<div class="empty-state">لا توجد إنذارات</div>';
            return;
        }

        const recentAlerts = window.alertManager ? window.alertManager.getRecentAlerts(10) : [];
        
        if (recentAlerts.length === 0) {
            alertsList.innerHTML = '<div class="empty-state">لا توجد إنذارات حديثة</div>';
            return;
        }

        alertsList.innerHTML = recentAlerts.map(alert => `
            <div class="alert-item ${alert.priority}">
                <div class="alert-icon">${this.getAlertIcon(alert.type)}</div>
                <div class="alert-info">
                    <h4>${this.getAlertTitle(alert.type)}</h4>
                    <p>${this.formatLocation(alert.location)}</p>
                </div>
                <div class="alert-time">
                    ${new Date(alert.timestamp).toLocaleString('ar-SA')}
                </div>
            </div>
        `).join('');
    }

    async loadAlertsData() {
        const tableBody = document.getElementById('alerts-table-body');

        if (!window.alertManager) {
            tableBody.innerHTML = '<tr><td colspan="7" class="empty-state">لا توجد إنذارات</td></tr>';
            return;
        }

        const alerts = window.alertManager ? window.alertManager.getRecentAlerts(100) : [];

        if (alerts.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="7" class="empty-state">لا توجد إنذارات</td></tr>';
            return;
        }

        tableBody.innerHTML = alerts.map(alert => `
            <tr>
                <td>
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <span>${this.getAlertIcon(alert.type)}</span>
                        ${this.getAlertTitle(alert.type)}
                        <span class="priority-indicator ${alert.priority || 'medium'}"></span>
                    </div>
                </td>
                <td>${this.formatLocation(alert.location)}</td>
                <td>${new Date(alert.timestamp).toLocaleString('ar-SA')}</td>
                <td><span class="status-badge ${alert.status}">${this.getStatusText(alert.status)}</span></td>
                <td>
                    <div class="user-info-cell">
                        <div class="user-name">${alert.senderInfo?.fullName || 'غير معروف'}</div>
                        <div class="user-phone">${alert.senderInfo?.phoneNumber || 'غير متاح'}</div>
                        <div class="user-ip">${alert.senderInfo?.ipAddress || 'غير معروف'}</div>
                    </div>
                </td>
                <td>
                    ${alert.customMessage ? `
                        <div class="custom-message-preview" title="${alert.customMessage}">
                            ${alert.customMessage.substring(0, 50)}${alert.customMessage.length > 50 ? '...' : ''}
                        </div>
                    ` : '<span class="no-message">لا توجد رسالة</span>'}
                </td>
                <td>
                    <div class="table-actions">
                        <button class="table-btn primary" onclick="adminDashboard.viewAlertDetails('${alert.id}')">
                            عرض
                        </button>
                        ${alert.status === 'active' ? `
                            <button class="table-btn" onclick="adminDashboard.resolveAlert('${alert.id}')">
                                حل
                            </button>
                        ` : ''}
                        <button class="table-btn secondary" onclick="adminDashboard.viewUserDetails('${alert.senderInfo?.userId || alert.userId}')">
                            المستخدم
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    async loadUsersData() {
        const tableBody = document.getElementById('users-table-body');
        
        // Simulate user data - replace with real API call
        const users = [
            {
                id: '1',
                name: 'مستخدم تجريبي',
                email: '<EMAIL>',
                role: 'user',
                lastActivity: new Date(),
                status: 'online',
                alertCount: 5
            }
        ];

        tableBody.innerHTML = users.map(user => `
            <tr>
                <td>
                    <div>
                        <strong>${user.name}</strong><br>
                        <small>${user.email}</small>
                    </div>
                </td>
                <td>${this.getRoleText(user.role)}</td>
                <td>${user.lastActivity.toLocaleString('ar-SA')}</td>
                <td><span class="status-badge ${user.status}">${this.getStatusText(user.status)}</span></td>
                <td>${user.alertCount}</td>
                <td>
                    <div class="table-actions">
                        <button class="table-btn primary" onclick="adminDashboard.editUser('${user.id}')">
                            تعديل
                        </button>
                        <button class="table-btn danger" onclick="adminDashboard.deleteUser('${user.id}')">
                            حذف
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    loadMapData() {
        // Map implementation would go here
        console.log('Loading map data...');
    }

    loadSettingsData() {
        // Load current settings into form
        document.getElementById('auto-notifications').checked = this.settings.autoNotifications;
        document.getElementById('sound-alerts').checked = this.settings.soundAlerts;
        document.getElementById('alert-radius').value = this.settings.alertRadius;
        document.getElementById('require-confirmation').checked = this.settings.requireConfirmation;
        document.getElementById('log-activities').checked = this.settings.logActivities;
        document.getElementById('refresh-interval').value = this.settings.refreshInterval;
        document.getElementById('max-alerts').value = this.settings.maxAlerts;
    }

    // Helper methods
    getAlertIcon(type) {
        const icons = {
            'ambulance': '🆘',
            'fire': '🔥',
            'civil-defense': '🛡️',
            'settler-attack': '⚠️',
            'settlers-present': '🚷',
            'intrusion': '🚨'
        };
        return icons[type] || '📢';
    }

    getAlertTitle(type) {
        const titles = {
            'ambulance': 'إسعاف',
            'fire': 'حريق',
            'civil-defense': 'دفاع مدني',
            'settler-attack': 'هجوم مستوطنين',
            'settlers-present': 'مستوطنين في الموقع',
            'intrusion': 'اقتحام'
        };
        return titles[type] || 'إنذار';
    }

    getStatusText(status) {
        const statusTexts = {
            'active': 'نشط',
            'resolved': 'محلول',
            'cancelled': 'ملغي',
            'online': 'متصل',
            'offline': 'غير متصل'
        };
        return statusTexts[status] || status;
    }

    getRoleText(role) {
        const roleTexts = {
            'admin': 'مسؤول',
            'moderator': 'مشرف',
            'user': 'مستخدم'
        };
        return roleTexts[role] || role;
    }

    formatLocation(location) {
        if (!location) return 'غير محدد';
        return `${location.latitude.toFixed(4)}, ${location.longitude.toFixed(4)}`;
    }

    getOnlineUsersCount() {
        // Simulate online users count
        return Math.floor(Math.random() * 50) + 10;
    }

    // Action methods
    viewAlertDetails(alertId) {
        const alert = this.findAlertById(alertId);
        if (!alert) {
            alert('الإنذار غير موجود');
            return;
        }

        const modal = document.getElementById('alert-details-modal');
        const content = document.getElementById('alert-details-content');

        content.innerHTML = `
            <div class="alert-detail-section">
                <h4>معلومات الإنذار</h4>
                <div class="detail-grid">
                    <div class="detail-item">
                        <span class="detail-label">نوع الإنذار</span>
                        <span class="detail-value text">${this.getAlertIcon(alert.type)} ${this.getAlertTitle(alert.type)}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">الأولوية</span>
                        <span class="detail-value text">
                            <span class="priority-indicator ${alert.priority || 'medium'}"></span>
                            ${this.getPriorityText(alert.priority)}
                        </span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">الحالة</span>
                        <span class="detail-value text">
                            <span class="status-badge ${alert.status}">${this.getStatusText(alert.status)}</span>
                        </span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">وقت الإرسال</span>
                        <span class="detail-value">${new Date(alert.timestamp).toLocaleString('ar-SA')}</span>
                    </div>
                </div>
            </div>

            ${alert.customMessage ? `
                <div class="alert-detail-section">
                    <h4>الرسالة المخصصة</h4>
                    <div class="custom-message-full">
                        "${alert.customMessage}"
                    </div>
                </div>
            ` : ''}

            <div class="alert-detail-section">
                <h4>معلومات الموقع</h4>
                <div class="detail-grid">
                    ${alert.location ? `
                        <div class="detail-item">
                            <span class="detail-label">خط العرض</span>
                            <span class="detail-value">${alert.location.latitude.toFixed(6)}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">خط الطول</span>
                            <span class="detail-value">${alert.location.longitude.toFixed(6)}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">دقة الموقع</span>
                            <span class="detail-value">${Math.round(alert.location.accuracy || 0)} متر</span>
                        </div>
                    ` : `
                        <div class="detail-item">
                            <span class="detail-label">الموقع</span>
                            <span class="detail-value text">غير محدد</span>
                        </div>
                    `}
                </div>
                ${alert.location ? `
                    <div style="margin-top: 1rem;">
                        <button class="action-btn primary" onclick="window.open('https://maps.google.com/maps?q=${alert.location.latitude},${alert.location.longitude}', '_blank')">
                            <span>🗺️</span>
                            عرض على الخريطة
                        </button>
                    </div>
                ` : ''}
            </div>

            <div class="user-tracking-info">
                <div class="tracking-warning">
                    🔍 معلومات المرسل (للمسؤولين فقط)
                </div>
                <div class="tracking-details">
                    <div class="tracking-item">
                        <span class="tracking-label">الاسم:</span>
                        <span class="tracking-value">${alert.senderInfo?.fullName || 'غير معروف'}</span>
                    </div>
                    <div class="tracking-item">
                        <span class="tracking-label">الهاتف:</span>
                        <span class="tracking-value">${alert.senderInfo?.phoneNumber || 'غير متاح'}</span>
                    </div>
                    <div class="tracking-item">
                        <span class="tracking-label">نوع الحساب:</span>
                        <span class="tracking-value">${this.getRoleText(alert.senderInfo?.userRole)}</span>
                    </div>
                    <div class="tracking-item">
                        <span class="tracking-label">المنطقة:</span>
                        <span class="tracking-value">${alert.senderInfo?.locationArea || 'غير محدد'}</span>
                    </div>
                    <div class="tracking-item">
                        <span class="tracking-label">عنوان IP:</span>
                        <span class="tracking-value">${alert.senderInfo?.ipAddress || 'غير معروف'}</span>
                    </div>
                    <div class="tracking-item">
                        <span class="tracking-label">الجهاز:</span>
                        <span class="tracking-value">${alert.senderInfo?.deviceInfo || 'غير معروف'}</span>
                    </div>
                </div>
            </div>

            <div class="alert-detail-section">
                <h4>إجراءات الإدارة</h4>
                <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                    ${alert.status === 'active' ? `
                        <button class="action-btn primary" onclick="adminDashboard.resolveAlert('${alert.id}'); adminDashboard.hideModal('alert-details-modal');">
                            <span>✅</span>
                            تم الحل
                        </button>
                        <button class="action-btn warning" onclick="adminDashboard.cancelAlert('${alert.id}'); adminDashboard.hideModal('alert-details-modal');">
                            <span>❌</span>
                            إلغاء
                        </button>
                    ` : ''}
                    <button class="action-btn secondary" onclick="adminDashboard.exportAlertData('${alert.id}')">
                        <span>📥</span>
                        تصدير البيانات
                    </button>
                    <button class="action-btn secondary" onclick="adminDashboard.viewUserDetails('${alert.senderInfo?.userId || alert.userId}')">
                        <span>👤</span>
                        ملف المستخدم
                    </button>
                </div>
            </div>
        `;

        this.showModal('alert-details-modal');
    }

    viewUserDetails(userId) {
        const user = this.findUserById(userId);
        if (!user) {
            alert('المستخدم غير موجود');
            return;
        }

        const userAlerts = this.getUserAlerts(userId);
        const modal = document.getElementById('user-modal');
        const title = document.getElementById('user-modal-title');
        const content = modal.querySelector('.modal-body');

        title.textContent = 'تفاصيل المستخدم';

        content.innerHTML = `
            <div class="user-tracking-info">
                <div class="tracking-warning">
                    👤 ملف المستخدم الكامل
                </div>
                <div class="tracking-details">
                    <div class="tracking-item">
                        <span class="tracking-label">الاسم الكامل:</span>
                        <span class="tracking-value">${user.fullName || 'غير معروف'}</span>
                    </div>
                    <div class="tracking-item">
                        <span class="tracking-label">رقم الهاتف:</span>
                        <span class="tracking-value">${user.phoneNumber || 'غير متاح'}</span>
                    </div>
                    <div class="tracking-item">
                        <span class="tracking-label">نوع الحساب:</span>
                        <span class="tracking-value">${this.getRoleText(user.userRole)}</span>
                    </div>
                    <div class="tracking-item">
                        <span class="tracking-label">المنطقة:</span>
                        <span class="tracking-value">${user.locationArea || 'غير محدد'}</span>
                    </div>
                    <div class="tracking-item">
                        <span class="tracking-label">تاريخ التسجيل:</span>
                        <span class="tracking-value">${new Date(user.registrationDate || user.timestamp).toLocaleDateString('ar-SA')}</span>
                    </div>
                    <div class="tracking-item">
                        <span class="tracking-label">آخر دخول:</span>
                        <span class="tracking-value">${new Date(user.lastLogin || user.lastActivity).toLocaleString('ar-SA')}</span>
                    </div>
                    <div class="tracking-item">
                        <span class="tracking-label">عنوان IP:</span>
                        <span class="tracking-value">${user.ipAddress || 'غير معروف'}</span>
                    </div>
                    <div class="tracking-item">
                        <span class="tracking-label">حالة التحقق:</span>
                        <span class="tracking-value">${user.isVerified ? '✅ محقق' : '❌ غير محقق'}</span>
                    </div>
                </div>
            </div>

            <div class="alert-detail-section">
                <h4>إحصائيات الإنذارات</h4>
                <div class="detail-grid">
                    <div class="detail-item">
                        <span class="detail-label">إجمالي الإنذارات</span>
                        <span class="detail-value">${userAlerts.length}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">الإنذارات النشطة</span>
                        <span class="detail-value">${userAlerts.filter(a => a.status === 'active').length}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">الإنذارات المحلولة</span>
                        <span class="detail-value">${userAlerts.filter(a => a.status === 'resolved').length}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">آخر إنذار</span>
                        <span class="detail-value">${userAlerts.length > 0 ? new Date(userAlerts[0].timestamp).toLocaleDateString('ar-SA') : 'لا يوجد'}</span>
                    </div>
                </div>
            </div>

            <div class="alert-detail-section">
                <h4>إجراءات المستخدم</h4>
                <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                    <button class="action-btn secondary" onclick="adminDashboard.contactUser('${user.phoneNumber}')">
                        <span>📞</span>
                        اتصال
                    </button>
                    <button class="action-btn secondary" onclick="adminDashboard.sendMessageToUser('${userId}')">
                        <span>💬</span>
                        إرسال رسالة
                    </button>
                    <button class="action-btn warning" onclick="adminDashboard.suspendUser('${userId}')">
                        <span>🚫</span>
                        إيقاف مؤقت
                    </button>
                    <button class="action-btn secondary" onclick="adminDashboard.exportUserData('${userId}')">
                        <span>📥</span>
                        تصدير البيانات
                    </button>
                </div>
            </div>
        `;

        this.showModal('user-modal');
    }

    resolveAlert(alertId) {
        if (window.alertManager) {
            window.alertManager.updateAlertStatus(alertId, 'resolved');
            this.loadAlertsData();
        }
    }

    editUser(userId) {
        console.log('Editing user:', userId);
        // Implementation for editing user
    }

    deleteUser(userId) {
        if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
            console.log('Deleting user:', userId);
            // Implementation for deleting user
        }
    }

    filterAlerts() {
        // Implementation for filtering alerts
        this.loadAlertsData();
    }

    saveSettings() {
        // Collect settings from form
        this.settings = {
            autoNotifications: document.getElementById('auto-notifications').checked,
            soundAlerts: document.getElementById('sound-alerts').checked,
            alertRadius: parseInt(document.getElementById('alert-radius').value),
            requireConfirmation: document.getElementById('require-confirmation').checked,
            logActivities: document.getElementById('log-activities').checked,
            refreshInterval: parseInt(document.getElementById('refresh-interval').value),
            maxAlerts: parseInt(document.getElementById('max-alerts').value)
        };

        // Save to localStorage
        localStorage.setItem('adminSettings', JSON.stringify(this.settings));
        
        // Update refresh interval
        this.startAutoRefresh();
        
        alert('تم حفظ الإعدادات بنجاح');
    }

    resetSettings() {
        if (confirm('هل أنت متأكد من إعادة تعيين الإعدادات؟')) {
            localStorage.removeItem('adminSettings');
            this.loadSettingsData();
            alert('تم إعادة تعيين الإعدادات');
        }
    }

    exportData() {
        const data = {
            alerts: window.alertManager ? window.alertManager.exportAlerts() : {},
            settings: this.settings,
            exportDate: new Date().toISOString()
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `emergency-shield-data-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }

    // Helper methods for user and alert management
    findAlertById(alertId) {
        if (!window.alertManager) return null;
        const alerts = window.alertManager.alerts || window.alertManager.getRecentAlerts(1000);
        return alerts.find(alert => alert.id === alertId);
    }

    findUserById(userId) {
        const users = JSON.parse(localStorage.getItem('emergencyShieldUsers') || '{}');
        return Object.values(users).find(user => user.userId === userId);
    }

    getUserAlerts(userId) {
        if (!window.alertManager) return [];
        const alerts = window.alertManager.alerts || window.alertManager.getRecentAlerts(1000);
        return alerts.filter(alert =>
            alert.userId === userId || alert.senderInfo?.userId === userId
        );
    }

    getPriorityText(priority) {
        const priorities = {
            'critical': 'حرج',
            'high': 'عالي',
            'medium': 'متوسط',
            'low': 'منخفض'
        };
        return priorities[priority] || 'متوسط';
    }

    cancelAlert(alertId) {
        if (window.alertManager) {
            window.alertManager.updateAlertStatus(alertId, 'cancelled');
            this.loadAlertsData();
            alert('تم إلغاء الإنذار');
        }
    }

    exportAlertData(alertId) {
        const alert = this.findAlertById(alertId);
        if (!alert) return;

        const data = {
            alert: alert,
            exportDate: new Date().toISOString(),
            exportedBy: 'admin'
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `alert-${alertId}-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }

    exportUserData(userId) {
        const user = this.findUserById(userId);
        const userAlerts = this.getUserAlerts(userId);

        if (!user) return;

        const data = {
            user: user,
            alerts: userAlerts,
            exportDate: new Date().toISOString(),
            exportedBy: 'admin'
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `user-${userId}-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }

    contactUser(phoneNumber) {
        if (phoneNumber && phoneNumber !== 'غير متاح') {
            window.open(`tel:${phoneNumber}`);
        } else {
            alert('رقم الهاتف غير متاح');
        }
    }

    sendMessageToUser(userId) {
        const user = this.findUserById(userId);
        if (!user || !user.phoneNumber) {
            alert('لا يمكن إرسال رسالة - رقم الهاتف غير متاح');
            return;
        }

        const message = prompt('أدخل الرسالة التي تريد إرسالها:');
        if (message && window.smsService) {
            window.smsService.sendSMS(user.phoneNumber, `رسالة من إدارة درع الطوارئ:\n\n${message}`)
                .then(() => {
                    alert('تم إرسال الرسالة بنجاح');
                })
                .catch(error => {
                    alert('فشل في إرسال الرسالة: ' + error.message);
                });
        }
    }

    suspendUser(userId) {
        if (confirm('هل أنت متأكد من إيقاف هذا المستخدم مؤقتاً؟')) {
            const users = JSON.parse(localStorage.getItem('emergencyShieldUsers') || '{}');
            if (users[userId]) {
                users[userId].suspended = true;
                users[userId].suspendedAt = new Date().toISOString();
                localStorage.setItem('emergencyShieldUsers', JSON.stringify(users));
                alert('تم إيقاف المستخدم مؤقتاً');
                this.hideModal('user-modal');
            }
        }
    }

    clearOldData() {
        if (confirm('هل أنت متأكد من مسح البيانات القديمة؟ هذا الإجراء لا يمكن التراجع عنه.')) {
            if (window.alertManager) {
                window.alertManager.cleanupOldAlerts();
            }
            alert('تم مسح البيانات القديمة');
            this.loadDashboardData();
        }
    }

    startAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }

        const interval = (this.settings.refreshInterval || 30) * 1000;
        this.refreshInterval = setInterval(() => {
            if (this.currentSection === 'dashboard') {
                this.loadDashboardData();
            }
        }, interval);
    }

    showModal(modalId) {
        document.getElementById(modalId).classList.remove('hidden');
    }

    hideModal(modalId) {
        document.getElementById(modalId).classList.add('hidden');
    }

    logout() {
        localStorage.removeItem('adminToken');
        localStorage.removeItem('adminTokenExpiry');
        localStorage.removeItem('isAdmin');
        
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
        
        this.isAuthenticated = false;
        this.showLoginScreen();
        this.setupLoginHandlers();
    }
}

// Initialize admin dashboard
document.addEventListener('DOMContentLoaded', () => {
    window.adminDashboard = new AdminDashboard();
});
