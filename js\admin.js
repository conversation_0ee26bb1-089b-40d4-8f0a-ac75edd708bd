// Admin Dashboard Controller
class AdminDashboard {
    constructor() {
        this.isAuthenticated = false;
        this.currentSection = 'dashboard';
        this.refreshInterval = null;
        this.settings = {
            autoNotifications: true,
            soundAlerts: true,
            alertRadius: 5000,
            requireConfirmation: true,
            logActivities: true,
            refreshInterval: 30,
            maxAlerts: 1000
        };
        
        this.init();
    }

    async init() {
        // Check authentication
        this.checkAuthentication();
        
        if (!this.isAuthenticated) {
            this.showLoginScreen();
            this.setupLoginHandlers();
        } else {
            this.showDashboard();
            this.setupDashboardHandlers();
            this.loadDashboardData();
            this.startAutoRefresh();
        }
    }

    checkAuthentication() {
        const token = localStorage.getItem('adminToken');
        const expiry = localStorage.getItem('adminTokenExpiry');
        
        if (token && expiry && new Date().getTime() < parseInt(expiry)) {
            this.isAuthenticated = true;
        }
    }

    showLoginScreen() {
        document.getElementById('login-screen').classList.remove('hidden');
        document.getElementById('admin-dashboard').classList.add('hidden');
    }

    showDashboard() {
        document.getElementById('login-screen').classList.add('hidden');
        document.getElementById('admin-dashboard').classList.remove('hidden');
    }

    setupLoginHandlers() {
        const loginForm = document.getElementById('login-form');
        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            await this.handleLogin(e);
        });
    }

    async handleLogin(e) {
        const formData = new FormData(e.target);
        const username = formData.get('username');
        const password = formData.get('password');
        const errorDiv = document.getElementById('login-error');

        try {
            // Simulate authentication - replace with real API call
            if (username === 'admin' && password === 'admin123') {
                // Set authentication
                const token = 'admin_' + Date.now();
                const expiry = new Date().getTime() + (24 * 60 * 60 * 1000); // 24 hours
                
                localStorage.setItem('adminToken', token);
                localStorage.setItem('adminTokenExpiry', expiry.toString());
                localStorage.setItem('isAdmin', 'true');
                
                this.isAuthenticated = true;
                this.showDashboard();
                this.setupDashboardHandlers();
                this.loadDashboardData();
                this.startAutoRefresh();
            } else {
                throw new Error('اسم المستخدم أو كلمة المرور غير صحيحة');
            }
        } catch (error) {
            errorDiv.textContent = error.message;
            errorDiv.classList.remove('hidden');
        }
    }

    setupDashboardHandlers() {
        // Navigation
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const section = e.currentTarget.dataset.section;
                this.switchSection(section);
            });
        });

        // Logout
        document.getElementById('logout-btn').addEventListener('click', () => {
            this.logout();
        });

        // Refresh data
        document.getElementById('refresh-data').addEventListener('click', () => {
            this.loadDashboardData();
        });

        // Modal handlers
        this.setupModalHandlers();

        // Settings handlers
        this.setupSettingsHandlers();

        // Filter handlers
        this.setupFilterHandlers();
    }

    setupModalHandlers() {
        // Close modal buttons
        document.querySelectorAll('.close-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const modal = e.target.closest('.modal');
                this.hideModal(modal.id);
            });
        });

        // Modal backdrop clicks
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.hideModal(modal.id);
                }
            });
        });
    }

    setupSettingsHandlers() {
        // Save settings
        document.getElementById('save-settings').addEventListener('click', () => {
            this.saveSettings();
        });

        // Reset settings
        document.getElementById('reset-settings').addEventListener('click', () => {
            this.resetSettings();
        });

        // Export data
        document.getElementById('export-data').addEventListener('click', () => {
            this.exportData();
        });

        // Clear old data
        document.getElementById('clear-old-data').addEventListener('click', () => {
            this.clearOldData();
        });
    }

    setupFilterHandlers() {
        // Alert filters
        document.getElementById('alert-filter')?.addEventListener('change', () => {
            this.filterAlerts();
        });

        document.getElementById('alert-type-filter')?.addEventListener('change', () => {
            this.filterAlerts();
        });
    }

    switchSection(sectionName) {
        // Update navigation
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');

        // Update sections
        document.querySelectorAll('.admin-section').forEach(section => {
            section.classList.remove('active');
        });
        document.getElementById(`${sectionName}-section`).classList.add('active');

        this.currentSection = sectionName;

        // Load section-specific data
        this.loadSectionData(sectionName);
    }

    async loadSectionData(sectionName) {
        switch (sectionName) {
            case 'dashboard':
                this.loadDashboardData();
                break;
            case 'alerts':
                this.loadAlertsData();
                break;
            case 'users':
                this.loadUsersData();
                break;
            case 'map':
                this.loadMapData();
                break;
            case 'settings':
                this.loadSettingsData();
                break;
        }
    }

    async loadDashboardData() {
        try {
            // Update last update time
            document.getElementById('last-update-time').textContent = 
                new Date().toLocaleString('ar-SA');

            // Load statistics
            await this.loadStatistics();
            
            // Load recent alerts
            await this.loadRecentAlerts();
            
        } catch (error) {
            console.error('Failed to load dashboard data:', error);
        }
    }

    async loadStatistics() {
        // Get statistics from alert manager
        const stats = window.alertManager ? window.alertManager.getStatistics() : {
            total: 0,
            active: 0,
            today: 0,
            thisWeek: 0,
            thisMonth: 0
        };

        // Update stat cards
        document.getElementById('active-alerts').textContent = stats.active || 0;
        document.getElementById('today-alerts').textContent = stats.today || 0;
        document.getElementById('online-users').textContent = this.getOnlineUsersCount();
        document.getElementById('resolved-alerts').textContent = 
            (stats.total || 0) - (stats.active || 0);
    }

    async loadRecentAlerts() {
        const alertsList = document.getElementById('recent-alerts-list');
        
        if (!window.alertManager) {
            alertsList.innerHTML = '<div class="empty-state">لا توجد إنذارات</div>';
            return;
        }

        const recentAlerts = window.alertManager.getRecentAlerts(10);
        
        if (recentAlerts.length === 0) {
            alertsList.innerHTML = '<div class="empty-state">لا توجد إنذارات حديثة</div>';
            return;
        }

        alertsList.innerHTML = recentAlerts.map(alert => `
            <div class="alert-item ${alert.priority}">
                <div class="alert-icon">${this.getAlertIcon(alert.type)}</div>
                <div class="alert-info">
                    <h4>${this.getAlertTitle(alert.type)}</h4>
                    <p>${this.formatLocation(alert.location)}</p>
                </div>
                <div class="alert-time">
                    ${new Date(alert.timestamp).toLocaleString('ar-SA')}
                </div>
            </div>
        `).join('');
    }

    async loadAlertsData() {
        const tableBody = document.getElementById('alerts-table-body');
        
        if (!window.alertManager) {
            tableBody.innerHTML = '<tr><td colspan="6" class="empty-state">لا توجد إنذارات</td></tr>';
            return;
        }

        const alerts = window.alertManager.getRecentAlerts(100);
        
        if (alerts.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="6" class="empty-state">لا توجد إنذارات</td></tr>';
            return;
        }

        tableBody.innerHTML = alerts.map(alert => `
            <tr>
                <td>
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <span>${this.getAlertIcon(alert.type)}</span>
                        ${this.getAlertTitle(alert.type)}
                        <span class="priority-indicator ${alert.priority}"></span>
                    </div>
                </td>
                <td>${this.formatLocation(alert.location)}</td>
                <td>${new Date(alert.timestamp).toLocaleString('ar-SA')}</td>
                <td><span class="status-badge ${alert.status}">${this.getStatusText(alert.status)}</span></td>
                <td>${alert.userId || 'غير معروف'}</td>
                <td>
                    <div class="table-actions">
                        <button class="table-btn primary" onclick="adminDashboard.viewAlertDetails('${alert.id}')">
                            عرض
                        </button>
                        ${alert.status === 'active' ? `
                            <button class="table-btn" onclick="adminDashboard.resolveAlert('${alert.id}')">
                                حل
                            </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `).join('');
    }

    async loadUsersData() {
        const tableBody = document.getElementById('users-table-body');
        
        // Simulate user data - replace with real API call
        const users = [
            {
                id: '1',
                name: 'مستخدم تجريبي',
                email: '<EMAIL>',
                role: 'user',
                lastActivity: new Date(),
                status: 'online',
                alertCount: 5
            }
        ];

        tableBody.innerHTML = users.map(user => `
            <tr>
                <td>
                    <div>
                        <strong>${user.name}</strong><br>
                        <small>${user.email}</small>
                    </div>
                </td>
                <td>${this.getRoleText(user.role)}</td>
                <td>${user.lastActivity.toLocaleString('ar-SA')}</td>
                <td><span class="status-badge ${user.status}">${this.getStatusText(user.status)}</span></td>
                <td>${user.alertCount}</td>
                <td>
                    <div class="table-actions">
                        <button class="table-btn primary" onclick="adminDashboard.editUser('${user.id}')">
                            تعديل
                        </button>
                        <button class="table-btn danger" onclick="adminDashboard.deleteUser('${user.id}')">
                            حذف
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    loadMapData() {
        // Map implementation would go here
        console.log('Loading map data...');
    }

    loadSettingsData() {
        // Load current settings into form
        document.getElementById('auto-notifications').checked = this.settings.autoNotifications;
        document.getElementById('sound-alerts').checked = this.settings.soundAlerts;
        document.getElementById('alert-radius').value = this.settings.alertRadius;
        document.getElementById('require-confirmation').checked = this.settings.requireConfirmation;
        document.getElementById('log-activities').checked = this.settings.logActivities;
        document.getElementById('refresh-interval').value = this.settings.refreshInterval;
        document.getElementById('max-alerts').value = this.settings.maxAlerts;
    }

    // Helper methods
    getAlertIcon(type) {
        const icons = {
            'ambulance': '🆘',
            'fire': '🔥',
            'civil-defense': '🛡️',
            'settler-attack': '⚠️',
            'settlers-present': '🚷',
            'intrusion': '🚨'
        };
        return icons[type] || '📢';
    }

    getAlertTitle(type) {
        const titles = {
            'ambulance': 'إسعاف',
            'fire': 'حريق',
            'civil-defense': 'دفاع مدني',
            'settler-attack': 'هجوم مستوطنين',
            'settlers-present': 'مستوطنين في الموقع',
            'intrusion': 'اقتحام'
        };
        return titles[type] || 'إنذار';
    }

    getStatusText(status) {
        const statusTexts = {
            'active': 'نشط',
            'resolved': 'محلول',
            'cancelled': 'ملغي',
            'online': 'متصل',
            'offline': 'غير متصل'
        };
        return statusTexts[status] || status;
    }

    getRoleText(role) {
        const roleTexts = {
            'admin': 'مسؤول',
            'moderator': 'مشرف',
            'user': 'مستخدم'
        };
        return roleTexts[role] || role;
    }

    formatLocation(location) {
        if (!location) return 'غير محدد';
        return `${location.latitude.toFixed(4)}, ${location.longitude.toFixed(4)}`;
    }

    getOnlineUsersCount() {
        // Simulate online users count
        return Math.floor(Math.random() * 50) + 10;
    }

    // Action methods
    viewAlertDetails(alertId) {
        console.log('Viewing alert details:', alertId);
        // Implementation for viewing alert details
    }

    resolveAlert(alertId) {
        if (window.alertManager) {
            window.alertManager.updateAlertStatus(alertId, 'resolved');
            this.loadAlertsData();
        }
    }

    editUser(userId) {
        console.log('Editing user:', userId);
        // Implementation for editing user
    }

    deleteUser(userId) {
        if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
            console.log('Deleting user:', userId);
            // Implementation for deleting user
        }
    }

    filterAlerts() {
        // Implementation for filtering alerts
        this.loadAlertsData();
    }

    saveSettings() {
        // Collect settings from form
        this.settings = {
            autoNotifications: document.getElementById('auto-notifications').checked,
            soundAlerts: document.getElementById('sound-alerts').checked,
            alertRadius: parseInt(document.getElementById('alert-radius').value),
            requireConfirmation: document.getElementById('require-confirmation').checked,
            logActivities: document.getElementById('log-activities').checked,
            refreshInterval: parseInt(document.getElementById('refresh-interval').value),
            maxAlerts: parseInt(document.getElementById('max-alerts').value)
        };

        // Save to localStorage
        localStorage.setItem('adminSettings', JSON.stringify(this.settings));
        
        // Update refresh interval
        this.startAutoRefresh();
        
        alert('تم حفظ الإعدادات بنجاح');
    }

    resetSettings() {
        if (confirm('هل أنت متأكد من إعادة تعيين الإعدادات؟')) {
            localStorage.removeItem('adminSettings');
            this.loadSettingsData();
            alert('تم إعادة تعيين الإعدادات');
        }
    }

    exportData() {
        const data = {
            alerts: window.alertManager ? window.alertManager.exportAlerts() : {},
            settings: this.settings,
            exportDate: new Date().toISOString()
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `emergency-shield-data-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }

    clearOldData() {
        if (confirm('هل أنت متأكد من مسح البيانات القديمة؟ هذا الإجراء لا يمكن التراجع عنه.')) {
            if (window.alertManager) {
                window.alertManager.cleanupOldAlerts();
            }
            alert('تم مسح البيانات القديمة');
            this.loadDashboardData();
        }
    }

    startAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }

        const interval = (this.settings.refreshInterval || 30) * 1000;
        this.refreshInterval = setInterval(() => {
            if (this.currentSection === 'dashboard') {
                this.loadDashboardData();
            }
        }, interval);
    }

    showModal(modalId) {
        document.getElementById(modalId).classList.remove('hidden');
    }

    hideModal(modalId) {
        document.getElementById(modalId).classList.add('hidden');
    }

    logout() {
        localStorage.removeItem('adminToken');
        localStorage.removeItem('adminTokenExpiry');
        localStorage.removeItem('isAdmin');
        
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
        
        this.isAuthenticated = false;
        this.showLoginScreen();
        this.setupLoginHandlers();
    }
}

// Initialize admin dashboard
document.addEventListener('DOMContentLoaded', () => {
    window.adminDashboard = new AdminDashboard();
});
