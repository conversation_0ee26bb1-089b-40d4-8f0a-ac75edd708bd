#!/bin/bash

# Emergency Shield Server Starter
# درع الطوارئ - مشغل الخادم

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Print banner
print_banner() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                        🛡️ درع الطوارئ                        ║"
    echo "║                    Emergency Shield v2.0                    ║"
    echo "║                                                              ║"
    echo "║              نظام إنذار ذكي وفعال للطوارئ                   ║"
    echo "║                                                              ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# Check if required files exist
check_files() {
    echo -e "${YELLOW}🔍 فحص الملفات المطلوبة...${NC}"
    
    local required_files=("index.html" "admin.html" "auth.html" "styles.css" "config.js" "fix-errors.js")
    local missing_files=()
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            missing_files+=("$file")
        fi
    done
    
    if [[ ${#missing_files[@]} -gt 0 ]]; then
        echo -e "${RED}❌ الملفات المفقودة:${NC}"
        for file in "${missing_files[@]}"; do
            echo -e "${RED}   - $file${NC}"
        done
        return 1
    fi
    
    echo -e "${GREEN}✅ جميع الملفات الأساسية موجودة${NC}"
    return 0
}

# Find available port
find_port() {
    local port=8000
    while [[ $port -lt 8100 ]]; do
        if ! lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            echo $port
            return 0
        fi
        ((port++))
    done
    echo 0
}

# Print URLs
print_urls() {
    local port=$1
    local base_url="http://localhost:$port"
    
    echo -e "${BLUE}"
    echo "📱 الروابط المتاحة:"
    echo "════════════════════════════════════════════════════════════════"
    echo "🏠 التطبيق الرئيسي:     $base_url"
    echo "🔐 صفحة المصادقة:      $base_url/auth.html"
    echo "🛡️ لوحة التحكم:        $base_url/admin.html"
    echo "🔔 عرض الإشعارات:      $base_url/notifications-demo.html"
    echo "🧪 اختبار سريع:        $base_url/quick-test.html"
    echo "✅ فحص نهائي:          $base_url/final-check.html"
    echo "════════════════════════════════════════════════════════════════"
    echo -e "${NC}"
}

# Print instructions
print_instructions() {
    echo -e "${GREEN}"
    echo "📋 تعليمات الاستخدام:"
    echo "════════════════════════════════════════════════════════════════"
    echo "1. 🔐 ابدأ بالتسجيل في صفحة المصادقة"
    echo "2. 📱 استخدم رقم هاتف صحيح (+970xxxxxxxxx)"
    echo "3. 🔢 أدخل أي رمز تحقق (6 أرقام)"
    echo "4. 🚨 ابدأ في إرسال الإنذارات"
    echo "5. 🛡️ استخدم لوحة التحكم (admin/admin123)"
    echo "════════════════════════════════════════════════════════════════"
    echo -e "${NC}"
}

# Print tips
print_tips() {
    echo -e "${PURPLE}"
    echo "💡 نصائح مفيدة:"
    echo "════════════════════════════════════════════════════════════════"
    echo "• اسمح للمتصفح بالوصول للموقع الجغرافي"
    echo "• اسمح بالإشعارات للحصول على تجربة كاملة"
    echo "• استخدم HTTPS للميزات المتقدمة"
    echo "• جرب صفحة الاختبار السريع للتأكد من عمل كل شيء"
    echo "════════════════════════════════════════════════════════════════"
    echo -e "${NC}"
}

# Open browser (cross-platform)
open_browser() {
    local url=$1
    echo -e "${CYAN}🌐 فتح المتصفح: $url${NC}"
    
    if command -v xdg-open > /dev/null; then
        xdg-open "$url" >/dev/null 2>&1 &
    elif command -v open > /dev/null; then
        open "$url" >/dev/null 2>&1 &
    elif command -v start > /dev/null; then
        start "$url" >/dev/null 2>&1 &
    else
        echo -e "${YELLOW}⚠️ لا يمكن فتح المتصفح تلقائياً. افتح الرابط يدوياً: $url${NC}"
    fi
}

# Start server with Python
start_python_server() {
    local port=$1
    echo -e "${GREEN}🐍 استخدام Python...${NC}"
    
    # Try Python 3 first, then Python
    if command -v python3 > /dev/null; then
        python3 -m http.server $port
    elif command -v python > /dev/null; then
        python -m http.server $port
    else
        return 1
    fi
}

# Start server with Node.js
start_node_server() {
    local port=$1
    echo -e "${GREEN}🟢 استخدام Node.js...${NC}"
    
    if command -v npx > /dev/null; then
        npx serve . -p $port
    elif command -v node > /dev/null && command -v npm > /dev/null; then
        npm install -g serve 2>/dev/null
        npx serve . -p $port
    else
        return 1
    fi
}

# Start server with PHP
start_php_server() {
    local port=$1
    echo -e "${GREEN}🐘 استخدام PHP...${NC}"
    
    if command -v php > /dev/null; then
        php -S localhost:$port
    else
        return 1
    fi
}

# Cleanup function
cleanup() {
    echo -e "\n${YELLOW}🛑 تم إيقاف الخادم${NC}"
    echo -e "${WHITE}🙏 شكراً لاستخدام درع الطوارئ!${NC}"
    exit 0
}

# Main function
main() {
    # Set up signal handlers
    trap cleanup SIGINT SIGTERM
    
    # Print banner
    print_banner
    
    # Check files
    if ! check_files; then
        echo -e "${RED}❌ لا يمكن تشغيل الخادم - ملفات مفقودة${NC}"
        exit 1
    fi
    
    # Find available port
    local port=$(find_port)
    if [[ $port -eq 0 ]]; then
        echo -e "${RED}❌ لا يمكن العثور على منفذ متاح${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ تم العثور على منفذ متاح: $port${NC}"
    
    # Print information
    print_urls $port
    print_instructions
    print_tips
    
    echo -e "${CYAN}🚀 تشغيل الخادم...${NC}"
    echo -e "${WHITE}🛑 اضغط Ctrl+C لإيقاف الخادم${NC}"
    echo
    
    # Open browser after 3 seconds
    (sleep 3 && open_browser "http://localhost:$port") &
    
    # Try to start server with available tools
    if start_python_server $port; then
        echo "Server started with Python"
    elif start_node_server $port; then
        echo "Server started with Node.js"
    elif start_php_server $port; then
        echo "Server started with PHP"
    else
        echo -e "${RED}❌ لم يتم العثور على Python أو Node.js أو PHP${NC}"
        echo
        echo -e "${YELLOW}📥 يرجى تثبيت أحد البرامج التالية:${NC}"
        echo "• Python 3: https://www.python.org/downloads/"
        echo "• Node.js: https://nodejs.org/"
        echo "• PHP: https://www.php.net/downloads/"
        echo
        echo -e "${BLUE}أو يمكنك فتح الملفات مباشرة في المتصفح:${NC}"
        echo "🌐 file://$(pwd)/index.html"
        exit 1
    fi
}

# Make script executable
chmod +x "$0" 2>/dev/null

# Run main function
main "$@"
