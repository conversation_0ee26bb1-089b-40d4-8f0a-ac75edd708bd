// Maps and Location Visualization
class MapManager {
    constructor() {
        this.map = null;
        this.markers = [];
        this.userLocationMarker = null;
        this.alertMarkers = [];
        this.isMapLoaded = false;
        this.mapContainer = null;
        
        this.init();
    }

    async init() {
        // Initialize map when needed
        this.setupMapContainer();
    }

    setupMapContainer() {
        // Create map container if it doesn't exist
        if (!document.getElementById('emergency-map')) {
            const mapDiv = document.createElement('div');
            mapDiv.id = 'emergency-map';
            mapDiv.style.cssText = `
                width: 100%;
                height: 400px;
                border-radius: 12px;
                overflow: hidden;
                background: #f3f4f6;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #666;
                font-size: 1rem;
            `;
            mapDiv.innerHTML = `
                <div style="text-align: center;">
                    <div style="font-size: 3rem; margin-bottom: 1rem;">🗺️</div>
                    <p>خريطة تفاعلية</p>
                    <p style="font-size: 0.875rem; opacity: 0.7;">سيتم إضافة الخريطة التفاعلية قريباً</p>
                </div>
            `;
            this.mapContainer = mapDiv;
        }
    }

    // Create a simple map visualization using HTML/CSS
    createSimpleMap(containerId, alerts = []) {
        const container = document.getElementById(containerId);
        if (!container) return;

        container.innerHTML = `
            <div class="simple-map">
                <div class="map-header">
                    <h3>خريطة الإنذارات</h3>
                    <div class="map-legend">
                        <div class="legend-item">
                            <span class="legend-color critical"></span>
                            <span>حرج</span>
                        </div>
                        <div class="legend-item">
                            <span class="legend-color high"></span>
                            <span>عالي</span>
                        </div>
                        <div class="legend-item">
                            <span class="legend-color medium"></span>
                            <span>متوسط</span>
                        </div>
                    </div>
                </div>
                <div class="map-grid" id="map-grid">
                    ${this.generateMapGrid(alerts)}
                </div>
                <div class="map-controls">
                    <button class="map-btn" onclick="mapManager.refreshMap()">
                        🔄 تحديث
                    </button>
                    <button class="map-btn" onclick="mapManager.centerOnUser()">
                        📍 موقعي
                    </button>
                    <button class="map-btn" onclick="mapManager.toggleHeatmap()">
                        🔥 خريطة الحرارة
                    </button>
                </div>
            </div>
        `;

        this.addMapStyles();
    }

    generateMapGrid(alerts) {
        // Create a simple grid representation
        const gridSize = 10;
        let grid = '';
        
        for (let i = 0; i < gridSize; i++) {
            for (let j = 0; j < gridSize; j++) {
                const hasAlert = Math.random() < 0.1; // 10% chance of alert
                const alertType = hasAlert ? this.getRandomAlertType() : null;
                const priority = hasAlert ? this.getRandomPriority() : null;
                
                grid += `
                    <div class="grid-cell ${hasAlert ? 'has-alert' : ''} ${priority || ''}" 
                         data-row="${i}" data-col="${j}"
                         ${hasAlert ? `title="${this.getAlertTitle(alertType)} - ${this.getPriorityText(priority)}"` : ''}>
                        ${hasAlert ? this.getAlertIcon(alertType) : ''}
                    </div>
                `;
            }
        }
        
        return grid;
    }

    addMapStyles() {
        if (document.getElementById('map-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'map-styles';
        styles.textContent = `
            .simple-map {
                background: white;
                border-radius: 12px;
                padding: 1rem;
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            }
            
            .map-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 1rem;
                flex-wrap: wrap;
                gap: 1rem;
            }
            
            .map-header h3 {
                margin: 0;
                font-size: 1.125rem;
                font-weight: 600;
            }
            
            .map-legend {
                display: flex;
                gap: 1rem;
                flex-wrap: wrap;
            }
            
            .legend-item {
                display: flex;
                align-items: center;
                gap: 0.25rem;
                font-size: 0.75rem;
            }
            
            .legend-color {
                width: 12px;
                height: 12px;
                border-radius: 50%;
            }
            
            .legend-color.critical {
                background: #dc2626;
            }
            
            .legend-color.high {
                background: #f59e0b;
            }
            
            .legend-color.medium {
                background: #3b82f6;
            }
            
            .map-grid {
                display: grid;
                grid-template-columns: repeat(10, 1fr);
                gap: 2px;
                background: #e5e7eb;
                padding: 4px;
                border-radius: 8px;
                margin-bottom: 1rem;
                aspect-ratio: 1;
            }
            
            .grid-cell {
                background: #f9fafb;
                border-radius: 2px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 0.75rem;
                cursor: pointer;
                transition: all 0.2s;
                position: relative;
            }
            
            .grid-cell:hover {
                background: #e5e7eb;
                transform: scale(1.1);
                z-index: 1;
            }
            
            .grid-cell.has-alert {
                animation: pulse 2s infinite;
            }
            
            .grid-cell.critical {
                background: rgba(220, 38, 38, 0.2);
                border: 1px solid #dc2626;
            }
            
            .grid-cell.high {
                background: rgba(245, 158, 11, 0.2);
                border: 1px solid #f59e0b;
            }
            
            .grid-cell.medium {
                background: rgba(59, 130, 246, 0.2);
                border: 1px solid #3b82f6;
            }
            
            .map-controls {
                display: flex;
                gap: 0.5rem;
                justify-content: center;
                flex-wrap: wrap;
            }
            
            .map-btn {
                background: #f3f4f6;
                border: 1px solid #d1d5db;
                border-radius: 6px;
                padding: 0.5rem 1rem;
                cursor: pointer;
                font-size: 0.875rem;
                transition: all 0.2s;
            }
            
            .map-btn:hover {
                background: #e5e7eb;
            }
            
            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.7; }
            }
            
            @media (max-width: 768px) {
                .map-header {
                    flex-direction: column;
                    align-items: flex-start;
                }
                
                .map-legend {
                    justify-content: center;
                    width: 100%;
                }
                
                .grid-cell {
                    font-size: 0.625rem;
                }
            }
        `;
        document.head.appendChild(styles);
    }

    // Helper methods
    getRandomAlertType() {
        const types = ['ambulance', 'fire', 'civil-defense', 'settler-attack', 'settlers-present', 'intrusion'];
        return types[Math.floor(Math.random() * types.length)];
    }

    getRandomPriority() {
        const priorities = ['critical', 'high', 'medium'];
        return priorities[Math.floor(Math.random() * priorities.length)];
    }

    getAlertIcon(type) {
        const icons = {
            'ambulance': '🆘',
            'fire': '🔥',
            'civil-defense': '🛡️',
            'settler-attack': '⚠️',
            'settlers-present': '🚷',
            'intrusion': '🚨'
        };
        return icons[type] || '📢';
    }

    getAlertTitle(type) {
        const titles = {
            'ambulance': 'إسعاف',
            'fire': 'حريق',
            'civil-defense': 'دفاع مدني',
            'settler-attack': 'هجوم مستوطنين',
            'settlers-present': 'مستوطنين في الموقع',
            'intrusion': 'اقتحام'
        };
        return titles[type] || 'إنذار';
    }

    getPriorityText(priority) {
        const texts = {
            'critical': 'حرج',
            'high': 'عالي',
            'medium': 'متوسط'
        };
        return texts[priority] || priority;
    }

    // Map control methods
    refreshMap() {
        const mapGrid = document.getElementById('map-grid');
        if (mapGrid) {
            mapGrid.innerHTML = this.generateMapGrid([]);
        }
    }

    centerOnUser() {
        if (window.locationManager && window.locationManager.currentLocation) {
            const location = window.locationManager.currentLocation;
            alert(`موقعك الحالي: ${location.latitude.toFixed(4)}, ${location.longitude.toFixed(4)}`);
        } else {
            alert('لم يتم تحديد موقعك بعد');
        }
    }

    toggleHeatmap() {
        const mapGrid = document.getElementById('map-grid');
        if (mapGrid) {
            mapGrid.classList.toggle('heatmap-mode');
            
            if (!document.getElementById('heatmap-styles')) {
                const heatmapStyles = document.createElement('style');
                heatmapStyles.id = 'heatmap-styles';
                heatmapStyles.textContent = `
                    .map-grid.heatmap-mode .grid-cell {
                        border-radius: 50%;
                    }
                    
                    .map-grid.heatmap-mode .grid-cell.has-alert {
                        box-shadow: 0 0 10px currentColor;
                    }
                    
                    .map-grid.heatmap-mode .grid-cell.critical {
                        background: radial-gradient(circle, rgba(220, 38, 38, 0.8) 0%, rgba(220, 38, 38, 0.2) 70%);
                    }
                    
                    .map-grid.heatmap-mode .grid-cell.high {
                        background: radial-gradient(circle, rgba(245, 158, 11, 0.8) 0%, rgba(245, 158, 11, 0.2) 70%);
                    }
                    
                    .map-grid.heatmap-mode .grid-cell.medium {
                        background: radial-gradient(circle, rgba(59, 130, 246, 0.8) 0%, rgba(59, 130, 246, 0.2) 70%);
                    }
                `;
                document.head.appendChild(heatmapStyles);
            }
        }
    }

    // Add alert to map
    addAlertToMap(alert) {
        if (!alert.location) return;

        // For now, just log the alert
        console.log('Adding alert to map:', alert);
        
        // In a real implementation, this would add a marker to the actual map
        this.refreshMap();
    }

    // Remove alert from map
    removeAlertFromMap(alertId) {
        console.log('Removing alert from map:', alertId);
        this.refreshMap();
    }

    // Get distance between two points
    calculateDistance(lat1, lon1, lat2, lon2) {
        const R = 6371e3; // Earth's radius in meters
        const φ1 = lat1 * Math.PI / 180;
        const φ2 = lat2 * Math.PI / 180;
        const Δφ = (lat2 - lat1) * Math.PI / 180;
        const Δλ = (lon2 - lon1) * Math.PI / 180;

        const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
                  Math.cos(φ1) * Math.cos(φ2) *
                  Math.sin(Δλ/2) * Math.sin(Δλ/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

        return R * c;
    }

    // Get alerts within radius
    getAlertsInRadius(centerLat, centerLon, radius) {
        if (!window.alertManager) return [];

        return window.alertManager.alerts.filter(alert => {
            if (!alert.location) return false;
            
            const distance = this.calculateDistance(
                centerLat, centerLon,
                alert.location.latitude, alert.location.longitude
            );
            
            return distance <= radius;
        });
    }

    // Export map data
    exportMapData() {
        const data = {
            alerts: window.alertManager ? window.alertManager.alerts : [],
            userLocation: window.locationManager ? window.locationManager.currentLocation : null,
            exportDate: new Date().toISOString()
        };

        return data;
    }
}

// Create global instance
window.mapManager = new MapManager();

// Initialize map when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Add map to quick actions if not already present
    const viewMapBtn = document.getElementById('view-map');
    if (viewMapBtn) {
        viewMapBtn.addEventListener('click', () => {
            // Create modal for map view
            const mapModal = document.createElement('div');
            mapModal.className = 'modal';
            mapModal.id = 'map-modal';
            mapModal.innerHTML = `
                <div class="modal-content large">
                    <div class="modal-header">
                        <h3>خريطة الإنذارات</h3>
                        <button class="close-btn" onclick="document.getElementById('map-modal').remove()">✕</button>
                    </div>
                    <div class="modal-body">
                        <div id="modal-map-container"></div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(mapModal);
            mapModal.classList.remove('hidden');
            
            // Initialize map in modal
            window.mapManager.createSimpleMap('modal-map-container');
            
            // Close on backdrop click
            mapModal.addEventListener('click', (e) => {
                if (e.target === mapModal) {
                    mapModal.remove();
                }
            });
        });
    }
});
