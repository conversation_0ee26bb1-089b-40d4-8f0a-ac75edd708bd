/* Authentication Styles */

.auth-body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.auth-container {
    max-width: 400px;
    width: 100%;
    margin: 1rem;
    position: relative;
}

.auth-screen {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    display: none;
    animation: slideIn 0.3s ease;
}

.auth-screen.active {
    display: block;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Header */
.auth-header {
    text-align: center;
    margin-bottom: 2rem;
    position: relative;
}

.back-btn {
    position: absolute;
    right: 0;
    top: 0;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: background 0.2s;
}

.back-btn:hover {
    background: rgba(0, 0, 0, 0.1);
}

.auth-header .shield-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
    animation: pulse 2s infinite;
}

.auth-header h1 {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 0.5rem;
}

.auth-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e3a8a;
    margin-bottom: 0.5rem;
}

.auth-header p {
    color: #666;
    margin-bottom: 0.5rem;
}

.subtitle {
    font-weight: 600;
    color: #dc2626 !important;
}

.success-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #16a34a;
}

/* Features */
.auth-features {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 2rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: #f8fafc;
    border-radius: 12px;
    font-size: 0.875rem;
    font-weight: 500;
}

.feature-icon {
    font-size: 1.25rem;
}

/* Forms */
.auth-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
}

.form-group input,
.form-group select {
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    transition: border-color 0.2s;
    background: white;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #3b82f6;
}

.phone-input {
    display: flex;
    gap: 0.5rem;
}

.phone-input select {
    flex: 0 0 auto;
    min-width: 100px;
}

.phone-input input {
    flex: 1;
}

/* Checkbox */
.checkbox-group {
    margin-top: 0.5rem;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    cursor: pointer;
    font-size: 0.875rem;
    line-height: 1.4;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    background: #e5e7eb;
    border-radius: 4px;
    position: relative;
    transition: background 0.2s;
    flex-shrink: 0;
    margin-top: 2px;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #3b82f6;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.75rem;
    font-weight: bold;
}

.checkbox-label a {
    color: #3b82f6;
    text-decoration: none;
}

.checkbox-label a:hover {
    text-decoration: underline;
}

/* Verification Input */
.verification-input {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    margin-bottom: 1rem;
}

.verification-input input {
    width: 50px;
    height: 50px;
    text-align: center;
    font-size: 1.25rem;
    font-weight: 600;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    transition: border-color 0.2s;
}

.verification-input input:focus {
    outline: none;
    border-color: #3b82f6;
}

.verification-info {
    text-align: center;
    margin-bottom: 1rem;
}

.verification-info p {
    color: #666;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

/* Buttons */
.auth-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
}

.auth-btn.primary {
    background: #dc2626;
    color: white;
}

.auth-btn.primary:hover {
    background: #b91c1c;
    transform: translateY(-1px);
}

.auth-btn.secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.auth-btn.secondary:hover {
    background: #e5e7eb;
}

.auth-btn.full-width {
    width: 100%;
}

.auth-btn span {
    font-size: 1.125rem;
}

.link-btn {
    background: none;
    border: none;
    color: #3b82f6;
    cursor: pointer;
    font-size: inherit;
    text-decoration: underline;
    padding: 0;
}

.link-btn:hover {
    color: #2563eb;
}

.link-btn:disabled {
    color: #9ca3af;
    cursor: not-allowed;
    text-decoration: none;
}

/* Actions */
.auth-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.auth-footer {
    text-align: center;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e5e7eb;
}

.auth-footer p {
    color: #666;
    font-size: 0.875rem;
}

/* User Info Display */
.user-info {
    background: #f8fafc;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.user-info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e5e7eb;
}

.user-info-item:last-child {
    border-bottom: none;
}

.user-info-label {
    font-weight: 600;
    color: #374151;
}

.user-info-value {
    color: #666;
}

/* Terms and Privacy Content */
.terms-content,
.privacy-content {
    max-height: 400px;
    overflow-y: auto;
    padding: 1rem 0;
}

.terms-content h4,
.privacy-content h4 {
    color: #1e3a8a;
    margin-top: 1.5rem;
    margin-bottom: 0.5rem;
}

.terms-content h4:first-child,
.privacy-content h4:first-child {
    margin-top: 0;
}

.terms-content p,
.privacy-content p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 480px) {
    .auth-container {
        margin: 0.5rem;
    }
    
    .auth-screen {
        padding: 1.5rem;
    }
    
    .auth-header h1 {
        font-size: 1.5rem;
    }
    
    .auth-header h2 {
        font-size: 1.25rem;
    }
    
    .auth-features {
        grid-template-columns: 1fr;
    }
    
    .phone-input {
        flex-direction: column;
    }
    
    .phone-input select {
        min-width: auto;
    }
    
    .verification-input {
        gap: 0.25rem;
    }
    
    .verification-input input {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}

/* Loading States */
.auth-btn.loading {
    opacity: 0.7;
    cursor: not-allowed;
}

.auth-btn.loading::after {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 0.5rem;
}

/* Error States */
.form-group.error input,
.form-group.error select {
    border-color: #dc2626;
}

.error-message {
    color: #dc2626;
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

/* Success States */
.form-group.success input,
.form-group.success select {
    border-color: #16a34a;
}

.success-message {
    color: #16a34a;
    font-size: 0.75rem;
    margin-top: 0.25rem;
}
