@echo off
chcp 65001 >nul
title بناء تطبيق درع الطوارئ - Emergency Shield Builder

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🛠️ بناء تطبيق درع الطوارئ                  ║
echo ║                Emergency Shield App Builder                  ║
echo ║                                                              ║
echo ║                     تحويل إلى تطبيق Windows                  ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo.
    echo 📥 يرجى تثبيت Node.js من:
    echo https://nodejs.org/
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js مثبت
node --version

REM Check if npm is available
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متاح
    pause
    exit /b 1
)

echo ✅ npm متاح
npm --version
echo.

echo 📦 تثبيت المتطلبات...
echo ════════════════════════════════════════════════════════════════

REM Install dependencies
echo 🔄 تثبيت Electron...
call npm install electron --save-dev
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت Electron
    pause
    exit /b 1
)

echo 🔄 تثبيت Electron Builder...
call npm install electron-builder --save-dev
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت Electron Builder
    pause
    exit /b 1
)

echo 🔄 تثبيت المكونات الإضافية...
call npm install electron-updater electron-log electron-store node-notifier --save
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت المكونات الإضافية
    pause
    exit /b 1
)

echo.
echo ✅ تم تثبيت جميع المتطلبات بنجاح
echo.

echo 🏗️ بناء التطبيق...
echo ════════════════════════════════════════════════════════════════

REM Create icons directory if it doesn't exist
if not exist "icons" (
    mkdir icons
    echo 📁 تم إنشاء مجلد الأيقونات
)

REM Copy default icon if needed
if not exist "icons\icon.ico" (
    echo 🎨 إنشاء أيقونة افتراضية...
    REM You would need to provide an actual icon file here
    echo ⚠️ يرجى إضافة ملف icon.ico في مجلد icons
)

echo.
echo 🚀 بدء عملية البناء...
echo.

REM Build for Windows 64-bit
echo 🔨 بناء إصدار Windows 64-bit...
call npm run build-win64
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء إصدار 64-bit
    goto :build_32bit
)
echo ✅ تم بناء إصدار 64-bit بنجاح

:build_32bit
echo.
echo 🔨 بناء إصدار Windows 32-bit...
call npm run build-win32
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء إصدار 32-bit
    goto :build_portable
)
echo ✅ تم بناء إصدار 32-bit بنجاح

:build_portable
echo.
echo 🔨 بناء إصدار محمول...
call npm run pack
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء الإصدار المحمول
    goto :finish
)
echo ✅ تم بناء الإصدار المحمول بنجاح

:finish
echo.
echo 🎉 اكتملت عملية البناء!
echo ════════════════════════════════════════════════════════════════

if exist "dist" (
    echo 📁 ملفات التطبيق متاحة في مجلد: dist\
    echo.
    echo 📋 الملفات المنشأة:
    dir /b dist\*.exe 2>nul
    dir /b dist\*.msi 2>nul
    echo.
    
    echo 💡 أنواع الملفات:
    echo • ملفات .exe - مثبت التطبيق
    echo • ملفات portable - إصدار محمول (لا يحتاج تثبيت)
    echo.
    
    echo 🚀 لتشغيل التطبيق:
    echo 1. ثبت الملف .exe للاستخدام العادي
    echo 2. أو شغل الإصدار المحمول مباشرة
    echo.
    
    set /p open_folder="هل تريد فتح مجلد الملفات؟ (y/n): "
    if /i "%open_folder%"=="y" (
        explorer dist
    )
) else (
    echo ❌ لم يتم العثور على مجلد dist
    echo تأكد من نجاح عملية البناء
)

echo.
echo 📞 للدعم الفني:
echo • راجع ملف README.md
echo • تحقق من ملف package.json
echo • تأكد من وجود جميع الملفات المطلوبة
echo.

pause
