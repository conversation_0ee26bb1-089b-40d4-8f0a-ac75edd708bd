// Alert Management System
class AlertManager {
    constructor() {
        this.alerts = [];
        this.activeAlerts = new Map();
        this.alertHistory = [];
        this.subscribers = [];
        this.maxAlerts = 1000;
        this.alertRadius = 5000; // 5km default radius
        
        this.init();
    }

    init() {
        this.loadAlertsFromStorage();
        this.setupEventListeners();
        
        // Start periodic cleanup
        setInterval(() => this.cleanupOldAlerts(), 300000); // Every 5 minutes
    }

    // Create new alert
    createAlert(type, location, additionalData = {}) {
        const alert = {
            id: this.generateAlertId(),
            type,
            location,
            timestamp: new Date().toISOString(),
            status: 'active',
            priority: this.getAlertPriority(type),
            userId: this.getUserId(),
            deviceId: this.getDeviceId(),
            ...additionalData
        };

        // Add to active alerts
        this.activeAlerts.set(alert.id, alert);
        this.alerts.unshift(alert);
        
        // Limit alerts array size
        if (this.alerts.length > this.maxAlerts) {
            this.alerts = this.alerts.slice(0, this.maxAlerts);
        }

        // Save to storage
        this.saveAlertsToStorage();
        
        // Notify subscribers
        this.notifySubscribers('alertCreated', alert);
        
        // Send to server if online
        if (navigator.onLine) {
            this.sendAlertToServer(alert);
        } else {
            this.queueAlertForSync(alert);
        }

        return alert;
    }

    // Get alert priority based on type
    getAlertPriority(type) {
        const priorities = {
            'ambulance': 'critical',
            'fire': 'critical',
            'settler-attack': 'critical',
            'intrusion': 'critical',
            'civil-defense': 'high',
            'settlers-present': 'medium'
        };
        return priorities[type] || 'medium';
    }

    // Generate unique alert ID
    generateAlertId() {
        return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    // Get user ID
    getUserId() {
        let userId = localStorage.getItem('userId');
        if (!userId) {
            userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            localStorage.setItem('userId', userId);
        }
        return userId;
    }

    // Get device ID
    getDeviceId() {
        let deviceId = localStorage.getItem('deviceId');
        if (!deviceId) {
            deviceId = `device_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            localStorage.setItem('deviceId', deviceId);
        }
        return deviceId;
    }

    // Update alert status
    updateAlertStatus(alertId, status, additionalData = {}) {
        const alert = this.activeAlerts.get(alertId);
        if (!alert) {
            throw new Error('Alert not found');
        }

        alert.status = status;
        alert.lastUpdated = new Date().toISOString();
        Object.assign(alert, additionalData);

        // If alert is resolved, move to history
        if (status === 'resolved' || status === 'cancelled') {
            this.activeAlerts.delete(alertId);
            this.alertHistory.unshift(alert);
        }

        this.saveAlertsToStorage();
        this.notifySubscribers('alertUpdated', alert);

        return alert;
    }

    // Get alerts within radius
    getAlertsInRadius(centerLocation, radius = this.alertRadius) {
        return this.alerts.filter(alert => {
            if (!alert.location || alert.status !== 'active') return false;
            
            const distance = this.calculateDistance(
                centerLocation.latitude,
                centerLocation.longitude,
                alert.location.latitude,
                alert.location.longitude
            );
            
            return distance <= radius;
        });
    }

    // Calculate distance between two points
    calculateDistance(lat1, lon1, lat2, lon2) {
        const R = 6371e3; // Earth's radius in meters
        const φ1 = lat1 * Math.PI / 180;
        const φ2 = lat2 * Math.PI / 180;
        const Δφ = (lat2 - lat1) * Math.PI / 180;
        const Δλ = (lon2 - lon1) * Math.PI / 180;

        const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
                  Math.cos(φ1) * Math.cos(φ2) *
                  Math.sin(Δλ/2) * Math.sin(Δλ/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

        return R * c;
    }

    // Get alerts by type
    getAlertsByType(type, status = 'active') {
        return this.alerts.filter(alert => 
            alert.type === type && alert.status === status
        );
    }

    // Get alerts by priority
    getAlertsByPriority(priority, status = 'active') {
        return this.alerts.filter(alert => 
            alert.priority === priority && alert.status === status
        );
    }

    // Get recent alerts
    getRecentAlerts(limit = 50) {
        return this.alerts.slice(0, limit);
    }

    // Search alerts
    searchAlerts(query, filters = {}) {
        let results = this.alerts;

        // Apply filters
        if (filters.type) {
            results = results.filter(alert => alert.type === filters.type);
        }
        if (filters.status) {
            results = results.filter(alert => alert.status === filters.status);
        }
        if (filters.priority) {
            results = results.filter(alert => alert.priority === filters.priority);
        }
        if (filters.dateFrom) {
            results = results.filter(alert => 
                new Date(alert.timestamp) >= new Date(filters.dateFrom)
            );
        }
        if (filters.dateTo) {
            results = results.filter(alert => 
                new Date(alert.timestamp) <= new Date(filters.dateTo)
            );
        }

        // Apply text search
        if (query) {
            const searchTerm = query.toLowerCase();
            results = results.filter(alert => 
                alert.type.toLowerCase().includes(searchTerm) ||
                (alert.description && alert.description.toLowerCase().includes(searchTerm)) ||
                alert.id.toLowerCase().includes(searchTerm)
            );
        }

        return results;
    }

    // Subscribe to alert events
    subscribe(callback) {
        this.subscribers.push(callback);
        return () => {
            const index = this.subscribers.indexOf(callback);
            if (index > -1) {
                this.subscribers.splice(index, 1);
            }
        };
    }

    // Notify subscribers
    notifySubscribers(event, data) {
        this.subscribers.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('Subscriber error:', error);
            }
        });
    }

    // Send alert to server
    async sendAlertToServer(alert) {
        try {
            const response = await fetch('/api/alerts', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                },
                body: JSON.stringify(alert)
            });

            if (!response.ok) {
                throw new Error(`Server error: ${response.status}`);
            }

            const result = await response.json();
            
            // Update alert with server response
            if (result.id) {
                alert.serverId = result.id;
                alert.synced = true;
                this.saveAlertsToStorage();
            }

            return result;
        } catch (error) {
            console.error('Failed to send alert to server:', error);
            this.queueAlertForSync(alert);
            throw error;
        }
    }

    // Queue alert for sync when online
    queueAlertForSync(alert) {
        let syncQueue = JSON.parse(localStorage.getItem('alertSyncQueue') || '[]');
        syncQueue.push(alert);
        localStorage.setItem('alertSyncQueue', JSON.stringify(syncQueue));
    }

    // Sync queued alerts
    async syncQueuedAlerts() {
        const syncQueue = JSON.parse(localStorage.getItem('alertSyncQueue') || '[]');
        if (syncQueue.length === 0) return;

        const synced = [];
        for (const alert of syncQueue) {
            try {
                await this.sendAlertToServer(alert);
                synced.push(alert);
            } catch (error) {
                console.error('Failed to sync alert:', alert.id, error);
            }
        }

        // Remove synced alerts from queue
        const remaining = syncQueue.filter(alert => !synced.includes(alert));
        localStorage.setItem('alertSyncQueue', JSON.stringify(remaining));
    }

    // Get auth token
    getAuthToken() {
        return localStorage.getItem('authToken') || '';
    }

    // Setup event listeners
    setupEventListeners() {
        // Online/offline events
        window.addEventListener('online', () => {
            this.syncQueuedAlerts();
        });

        // Visibility change (app becomes active)
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.refreshAlerts();
            }
        });
    }

    // Refresh alerts from server
    async refreshAlerts() {
        if (!navigator.onLine) return;

        try {
            const response = await fetch('/api/alerts/recent', {
                headers: {
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });

            if (response.ok) {
                const serverAlerts = await response.json();
                this.mergeServerAlerts(serverAlerts);
            }
        } catch (error) {
            console.error('Failed to refresh alerts:', error);
        }
    }

    // Merge server alerts with local alerts
    mergeServerAlerts(serverAlerts) {
        const existingIds = new Set(this.alerts.map(alert => alert.serverId || alert.id));
        
        const newAlerts = serverAlerts.filter(alert => 
            !existingIds.has(alert.id)
        );

        if (newAlerts.length > 0) {
            this.alerts.unshift(...newAlerts);
            this.saveAlertsToStorage();
            
            // Notify about new alerts
            newAlerts.forEach(alert => {
                this.notifySubscribers('alertReceived', alert);
            });
        }
    }

    // Clean up old alerts
    cleanupOldAlerts() {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - 7); // Keep alerts for 7 days

        const before = this.alerts.length;
        this.alerts = this.alerts.filter(alert => 
            new Date(alert.timestamp) > cutoffDate
        );

        if (this.alerts.length !== before) {
            this.saveAlertsToStorage();
        }
    }

    // Save alerts to localStorage
    saveAlertsToStorage() {
        try {
            const data = {
                alerts: this.alerts.slice(0, this.maxAlerts),
                activeAlerts: Array.from(this.activeAlerts.entries()),
                alertHistory: this.alertHistory.slice(0, 500)
            };
            localStorage.setItem('emergencyAlerts', JSON.stringify(data));
        } catch (error) {
            console.error('Failed to save alerts:', error);
        }
    }

    // Load alerts from localStorage
    loadAlertsFromStorage() {
        try {
            const stored = localStorage.getItem('emergencyAlerts');
            if (stored) {
                const data = JSON.parse(stored);
                this.alerts = data.alerts || [];
                this.activeAlerts = new Map(data.activeAlerts || []);
                this.alertHistory = data.alertHistory || [];
            }
        } catch (error) {
            console.error('Failed to load alerts:', error);
            this.alerts = [];
            this.activeAlerts = new Map();
            this.alertHistory = [];
        }
    }

    // Export alerts data
    exportAlerts() {
        return {
            alerts: this.alerts,
            activeAlerts: Array.from(this.activeAlerts.entries()),
            alertHistory: this.alertHistory,
            exportDate: new Date().toISOString()
        };
    }

    // Import alerts data
    importAlerts(data) {
        if (data.alerts) {
            this.alerts = data.alerts;
        }
        if (data.activeAlerts) {
            this.activeAlerts = new Map(data.activeAlerts);
        }
        if (data.alertHistory) {
            this.alertHistory = data.alertHistory;
        }
        this.saveAlertsToStorage();
    }

    // Get statistics
    getStatistics() {
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const thisWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

        return {
            total: this.alerts.length,
            active: this.activeAlerts.size,
            today: this.alerts.filter(alert => 
                new Date(alert.timestamp) >= today
            ).length,
            thisWeek: this.alerts.filter(alert => 
                new Date(alert.timestamp) >= thisWeek
            ).length,
            thisMonth: this.alerts.filter(alert => 
                new Date(alert.timestamp) >= thisMonth
            ).length,
            byType: this.getAlertCountsByType(),
            byPriority: this.getAlertCountsByPriority()
        };
    }

    getAlertCountsByType() {
        const counts = {};
        this.alerts.forEach(alert => {
            counts[alert.type] = (counts[alert.type] || 0) + 1;
        });
        return counts;
    }

    getAlertCountsByPriority() {
        const counts = {};
        this.alerts.forEach(alert => {
            counts[alert.priority] = (counts[alert.priority] || 0) + 1;
        });
        return counts;
    }
}

// Create global instance
window.alertManager = new AlertManager();
