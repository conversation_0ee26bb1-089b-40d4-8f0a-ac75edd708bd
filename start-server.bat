@echo off
chcp 65001 >nul
title درع الطوارئ - Emergency Shield Server

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        🛡️ درع الطوارئ                        ║
echo ║                    Emergency Shield v2.0                    ║
echo ║                                                              ║
echo ║              نظام إنذار ذكي وفعال للطوارئ                   ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 فحص الملفات المطلوبة...

if not exist "index.html" (
    echo ❌ ملف index.html مفقود
    goto :error
)

if not exist "admin.html" (
    echo ❌ ملف admin.html مفقود
    goto :error
)

if not exist "auth.html" (
    echo ❌ ملف auth.html مفقود
    goto :error
)

if not exist "styles.css" (
    echo ❌ ملف styles.css مفقود
    goto :error
)

echo ✅ جميع الملفات الأساسية موجودة

echo.
echo 🚀 تشغيل الخادم...
echo.

REM Try Python 3 first
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo 🐍 استخدام Python...
    echo.
    echo 📱 الروابط المتاحة:
    echo ════════════════════════════════════════════════════════════════
    echo 🏠 التطبيق الرئيسي:     http://localhost:8000
    echo 🔐 صفحة المصادقة:      http://localhost:8000/auth.html
    echo 🛡️ لوحة التحكم:        http://localhost:8000/admin.html
    echo 🔔 عرض الإشعارات:      http://localhost:8000/notifications-demo.html
    echo 🧪 اختبار سريع:        http://localhost:8000/quick-test.html
    echo ✅ فحص نهائي:          http://localhost:8000/final-check.html
    echo ════════════════════════════════════════════════════════════════
    echo.
    echo 📋 تعليمات الاستخدام:
    echo ════════════════════════════════════════════════════════════════
    echo 1. 🔐 ابدأ بالتسجيل في صفحة المصادقة
    echo 2. 📱 استخدم رقم هاتف صحيح (+970xxxxxxxxx)
    echo 3. 🔢 أدخل أي رمز تحقق (6 أرقام)
    echo 4. 🚨 ابدأ في إرسال الإنذارات
    echo 5. 🛡️ استخدم لوحة التحكم (admin/admin123)
    echo ════════════════════════════════════════════════════════════════
    echo.
    echo 💡 نصائح مفيدة:
    echo • اسمح للمتصفح بالوصول للموقع الجغرافي
    echo • اسمح بالإشعارات للحصول على تجربة كاملة
    echo • استخدم HTTPS للميزات المتقدمة
    echo.
    echo 🌐 سيتم فتح المتصفح تلقائياً...
    echo 🛑 اضغط Ctrl+C لإيقاف الخادم
    echo.
    
    REM Open browser after 3 seconds
    timeout /t 3 /nobreak >nul
    start http://localhost:8000
    
    python -m http.server 8000
    goto :end
)

REM Try Node.js
where node >nul 2>&1
if %errorlevel% == 0 (
    echo 🟢 استخدام Node.js...
    echo.
    echo 📱 الروابط المتاحة:
    echo ════════════════════════════════════════════════════════════════
    echo 🏠 التطبيق الرئيسي:     http://localhost:8000
    echo 🔐 صفحة المصادقة:      http://localhost:8000/auth.html
    echo 🛡️ لوحة التحكم:        http://localhost:8000/admin.html
    echo 🔔 عرض الإشعارات:      http://localhost:8000/notifications-demo.html
    echo 🧪 اختبار سريع:        http://localhost:8000/quick-test.html
    echo ✅ فحص نهائي:          http://localhost:8000/final-check.html
    echo ════════════════════════════════════════════════════════════════
    echo.
    echo 🌐 سيتم فتح المتصفح تلقائياً...
    echo 🛑 اضغط Ctrl+C لإيقاف الخادم
    echo.
    
    timeout /t 3 /nobreak >nul
    start http://localhost:8000
    
    npx serve . -p 8000
    goto :end
)

REM Try PHP
where php >nul 2>&1
if %errorlevel% == 0 (
    echo 🐘 استخدام PHP...
    echo.
    echo 📱 الروابط المتاحة:
    echo ════════════════════════════════════════════════════════════════
    echo 🏠 التطبيق الرئيسي:     http://localhost:8000
    echo 🔐 صفحة المصادقة:      http://localhost:8000/auth.html
    echo 🛡️ لوحة التحكم:        http://localhost:8000/admin.html
    echo 🔔 عرض الإشعارات:      http://localhost:8000/notifications-demo.html
    echo 🧪 اختبار سريع:        http://localhost:8000/quick-test.html
    echo ✅ فحص نهائي:          http://localhost:8000/final-check.html
    echo ════════════════════════════════════════════════════════════════
    echo.
    echo 🌐 سيتم فتح المتصفح تلقائياً...
    echo 🛑 اضغط Ctrl+C لإيقاف الخادم
    echo.
    
    timeout /t 3 /nobreak >nul
    start http://localhost:8000
    
    php -S localhost:8000
    goto :end
)

echo ❌ لم يتم العثور على Python أو Node.js أو PHP
echo.
echo 📥 يرجى تثبيت أحد البرامج التالية:
echo • Python 3: https://www.python.org/downloads/
echo • Node.js: https://nodejs.org/
echo • PHP: https://www.php.net/downloads/
echo.
echo أو يمكنك فتح الملفات مباشرة في المتصفح:
echo 🌐 file:///%CD%/index.html
echo.
goto :error

:error
echo.
echo ❌ حدث خطأ في تشغيل الخادم
echo 📞 للمساعدة، راجع ملف QUICK-START.md
echo.
pause
exit /b 1

:end
echo.
echo 🛑 تم إيقاف الخادم
echo 🙏 شكراً لاستخدام درع الطوارئ!
echo.
pause
