/* Admin Dashboard Styles */

/* Admin Body */
.admin-body {
    background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 100%);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    min-height: 100vh;
}

/* Login Screen */
.login-screen {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 1rem;
}

.login-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 400px;
    width: 100%;
}

.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.login-header .shield-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
}

.login-header h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 0.5rem;
}

.login-header p {
    color: #666;
    font-size: 1rem;
}

.login-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 600;
    color: #374151;
}

.form-group input {
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.2s;
}

.form-group input:focus {
    outline: none;
    border-color: #3b82f6;
}

.login-btn {
    background: #1e3a8a;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.2s;
}

.login-btn:hover {
    background: #1e40af;
}

.login-error {
    background: #fef2f2;
    color: #dc2626;
    padding: 0.75rem;
    border-radius: 8px;
    border: 1px solid #fecaca;
    font-size: 0.875rem;
}

/* Admin Dashboard */
.admin-dashboard {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Admin Header */
.admin-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 1rem 2rem;
}

.admin-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.admin-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.admin-logo .shield-icon {
    font-size: 2rem;
}

.admin-logo h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e3a8a;
    margin: 0;
}

.admin-actions {
    display: flex;
    gap: 1rem;
}

/* Navigation */
.admin-nav {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 1rem 2rem;
    display: flex;
    gap: 1rem;
    overflow-x: auto;
}

.nav-btn {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    white-space: nowrap;
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.nav-btn.active {
    background: rgba(255, 255, 255, 0.9);
    color: #1e3a8a;
}

.nav-btn span {
    font-size: 1.25rem;
}

/* Main Content */
.admin-main {
    flex: 1;
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

.admin-section {
    display: none;
}

.admin-section.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.section-header h2 {
    font-size: 1.75rem;
    font-weight: 700;
    color: white;
    margin: 0;
}

.section-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.last-update {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.875rem;
}

/* Action Buttons */
.action-btn {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: none;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.action-btn.primary {
    background: #3b82f6;
}

.action-btn.primary:hover {
    background: #2563eb;
}

.action-btn.secondary {
    background: #6b7280;
}

.action-btn.secondary:hover {
    background: #4b5563;
}

.action-btn.warning {
    background: #f59e0b;
}

.action-btn.warning:hover {
    background: #d97706;
}

.action-btn span {
    font-size: 1.125rem;
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-card.critical {
    border-left: 4px solid #dc2626;
}

.stat-card.warning {
    border-left: 4px solid #f59e0b;
}

.stat-card.info {
    border-left: 4px solid #3b82f6;
}

.stat-card.success {
    border-left: 4px solid #10b981;
}

.stat-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.stat-content h3 {
    font-size: 0.875rem;
    font-weight: 600;
    color: #666;
    margin: 0 0 0.5rem 0;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
}

/* Dashboard Sections */
.dashboard-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.dashboard-section h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 1rem 0;
}

/* Alerts List */
.alerts-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.alert-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f9fafb;
    border-radius: 12px;
    border-right: 4px solid #e5e7eb;
}

.alert-item.critical {
    border-right-color: #dc2626;
}

.alert-item.high {
    border-right-color: #f59e0b;
}

.alert-item.medium {
    border-right-color: #3b82f6;
}

.alert-icon {
    font-size: 1.5rem;
}

.alert-info {
    flex: 1;
}

.alert-info h4 {
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 0.25rem 0;
}

.alert-info p {
    font-size: 0.875rem;
    color: #666;
    margin: 0;
}

.alert-time {
    font-size: 0.75rem;
    color: #9ca3af;
}

/* Tables */
.table-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
}

.admin-table th {
    background: #f8fafc;
    padding: 1rem;
    text-align: right;
    font-weight: 600;
    color: #374151;
    border-bottom: 1px solid #e5e7eb;
}

.admin-table td {
    padding: 1rem;
    border-bottom: 1px solid #f3f4f6;
    color: #1f2937;
}

.admin-table tr:hover {
    background: #f9fafb;
}

/* Filters */
.filter-select {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    padding: 0.5rem 1rem;
    color: #1f2937;
    cursor: pointer;
}

/* Map */
.admin-map {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    height: 500px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
}

.map-placeholder {
    text-align: center;
    color: #666;
}

.map-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

/* Settings */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.settings-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.settings-card h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 1rem 0;
}

.setting-item {
    margin-bottom: 1rem;
}

.setting-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-weight: 500;
}

.setting-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    background: #e5e7eb;
    border-radius: 4px;
    position: relative;
    transition: background 0.2s;
}

.setting-label input[type="checkbox"]:checked + .checkmark {
    background: #3b82f6;
}

.setting-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.75rem;
    font-weight: bold;
}

.setting-item label:not(.setting-label) {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
}

.setting-item input[type="number"] {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.875rem;
}

.settings-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* Chart Container */
.chart-container {
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f9fafb;
    border-radius: 12px;
    color: #666;
}

/* Modal Overrides for Admin */
.modal-content.large {
    max-width: 800px;
}

/* Status Badges */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.active {
    background: #fef2f2;
    color: #dc2626;
}

.status-badge.resolved {
    background: #f0fdf4;
    color: #16a34a;
}

.status-badge.cancelled {
    background: #f3f4f6;
    color: #6b7280;
}

.status-badge.online {
    background: #f0fdf4;
    color: #16a34a;
}

.status-badge.offline {
    background: #f3f4f6;
    color: #6b7280;
}

/* Priority Indicators */
.priority-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-left: 0.5rem;
}

.priority-indicator.critical {
    background: #dc2626;
}

.priority-indicator.high {
    background: #f59e0b;
}

.priority-indicator.medium {
    background: #3b82f6;
}

.priority-indicator.low {
    background: #10b981;
}

/* Action Buttons in Tables */
.table-actions {
    display: flex;
    gap: 0.5rem;
}

.table-btn {
    background: none;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    padding: 0.25rem 0.5rem;
    cursor: pointer;
    font-size: 0.75rem;
    transition: all 0.2s;
}

.table-btn:hover {
    background: #f3f4f6;
}

.table-btn.primary {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
}

.table-btn.primary:hover {
    background: #2563eb;
}

.table-btn.danger {
    background: #dc2626;
    color: white;
    border-color: #dc2626;
}

.table-btn.danger:hover {
    background: #b91c1c;
}

/* Loading States */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: #6b7280;
}

.loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 0.5rem;
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6b7280;
}

.empty-state-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-header {
        padding: 1rem;
    }

    .admin-header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .admin-nav {
        padding: 1rem;
        justify-content: flex-start;
    }

    .admin-main {
        padding: 1rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .settings-grid {
        grid-template-columns: 1fr;
    }

    .admin-table {
        font-size: 0.875rem;
    }

    .admin-table th,
    .admin-table td {
        padding: 0.75rem 0.5rem;
    }

    .table-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .login-container {
        padding: 1.5rem;
    }

    .nav-btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    .stat-card {
        padding: 1rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .admin-table {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
    }
}

/* User Info Cell Styles */
.user-info-cell {
    min-width: 200px;
}

.user-name {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.user-phone {
    font-size: 0.75rem;
    color: #3b82f6;
    font-family: monospace;
    margin-bottom: 0.25rem;
}

.user-ip {
    font-size: 0.75rem;
    color: #6b7280;
    font-family: monospace;
}

/* Custom Message Preview */
.custom-message-preview {
    max-width: 200px;
    font-size: 0.875rem;
    color: #374151;
    background: #f8fafc;
    padding: 0.5rem;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
    cursor: help;
}

.no-message {
    color: #9ca3af;
    font-style: italic;
    font-size: 0.75rem;
}

/* Enhanced Alert Details Modal */
.alert-details-content {
    max-height: 600px;
    overflow-y: auto;
}

.alert-detail-section {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.alert-detail-section h4 {
    margin: 0 0 1rem 0;
    color: #1e3a8a;
    font-size: 1rem;
    font-weight: 600;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.detail-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
}

.detail-value {
    font-size: 0.875rem;
    color: #1f2937;
    font-family: monospace;
}

.detail-value.text {
    font-family: inherit;
}

/* User Tracking Styles */
.user-tracking-info {
    background: #fef7f0;
    border: 1px solid #fed7aa;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.tracking-warning {
    color: #ea580c;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.tracking-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.75rem;
    font-size: 0.75rem;
}

.tracking-item {
    display: flex;
    justify-content: space-between;
    padding: 0.25rem 0;
    border-bottom: 1px solid #fed7aa;
}

.tracking-item:last-child {
    border-bottom: none;
}

.tracking-label {
    font-weight: 600;
    color: #9a3412;
}

.tracking-value {
    color: #ea580c;
    font-family: monospace;
}
