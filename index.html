<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>درع الطوارئ - أمنك بضغطة</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#dc2626">
    <meta name="description" content="نظام إنذار ذكي للطوارئ - أرسل إنذارات فورية بضغطة زر">
    
    <!-- PWA Meta Tags -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="درع الطوارئ">
    
    <!-- Icons -->
    <link rel="icon" type="image/png" sizes="32x32" href="icons/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="icons/favicon-16x16.png">
    <link rel="apple-touch-icon" href="icons/apple-touch-icon.png">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="shield-icon">🛡️</div>
            <h2>درع الطوارئ</h2>
            <div class="loading-spinner"></div>
            <p>جاري التحميل...</p>
        </div>
    </div>

    <!-- Main App -->
    <div id="app" class="app hidden">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <span class="shield-icon">🛡️</span>
                    <h1>درع الطوارئ</h1>
                </div>
                <div class="status-indicator">
                    <span id="connection-status" class="status-dot online"></span>
                    <span class="status-text">متصل</span>
                </div>
            </div>
        </header>

        <!-- Location Section -->
        <section class="location-section">
            <div class="location-card">
                <div class="location-header">
                    <span class="location-icon">📍</span>
                    <h3>موقعك الحالي</h3>
                    <button id="refresh-location" class="refresh-btn">🔄</button>
                </div>
                <div id="location-display" class="location-display">
                    <p class="location-text">جاري تحديد الموقع...</p>
                    <button id="manual-location" class="manual-location-btn">تحديد الموقع يدوياً</button>
                </div>
            </div>
        </section>

        <!-- Emergency Buttons -->
        <main class="emergency-grid">
            <button class="emergency-btn ambulance" data-type="ambulance">
                <div class="btn-icon">🆘</div>
                <div class="btn-text">
                    <h3>إسعاف</h3>
                    <p>حالة طبية طارئة</p>
                </div>
                <div class="btn-pulse"></div>
            </button>

            <button class="emergency-btn fire" data-type="fire">
                <div class="btn-icon">🔥</div>
                <div class="btn-text">
                    <h3>حريق</h3>
                    <p>حريق في المنطقة</p>
                </div>
                <div class="btn-pulse"></div>
            </button>

            <button class="emergency-btn civil-defense" data-type="civil-defense">
                <div class="btn-icon">🛡️</div>
                <div class="btn-text">
                    <h3>دفاع مدني</h3>
                    <p>طوارئ عامة</p>
                </div>
                <div class="btn-pulse"></div>
            </button>

            <button class="emergency-btn settler-attack" data-type="settler-attack">
                <div class="btn-icon">⚠️</div>
                <div class="btn-text">
                    <h3>هجوم مستوطنين</h3>
                    <p>هجوم في المنطقة</p>
                </div>
                <div class="btn-pulse"></div>
            </button>

            <button class="emergency-btn settlers-present" data-type="settlers-present">
                <div class="btn-icon">🚷</div>
                <div class="btn-text">
                    <h3>مستوطنين في الموقع</h3>
                    <p>وجود مستوطنين</p>
                </div>
                <div class="btn-pulse"></div>
            </button>

            <button class="emergency-btn intrusion" data-type="intrusion">
                <div class="btn-icon">🚨</div>
                <div class="btn-text">
                    <h3>إنذار اقتحام</h3>
                    <p>اقتحام للمنطقة</p>
                </div>
                <div class="btn-pulse"></div>
            </button>
        </main>

        <!-- Quick Actions -->
        <section class="quick-actions">
            <button id="view-map" class="action-btn">
                <span>🗺️</span>
                عرض الخريطة
            </button>
            <button id="recent-alerts" class="action-btn">
                <span>📋</span>
                الإنذارات الأخيرة
            </button>
            <button id="admin-panel" class="action-btn admin-only hidden">
                <span>⚙️</span>
                لوحة التحكم
            </button>
        </section>
    </div>

    <!-- Confirmation Modal -->
    <div id="confirmation-modal" class="modal hidden">
        <div class="modal-content large">
            <div class="modal-header">
                <h3>تأكيد الإنذار</h3>
                <button id="close-modal" class="close-btn">✕</button>
            </div>
            <div class="modal-body">
                <div id="alert-preview" class="alert-preview">
                    <div class="alert-icon"></div>
                    <div class="alert-details">
                        <h4 id="alert-type"></h4>
                        <p id="alert-location"></p>
                        <p class="alert-time"></p>
                    </div>
                </div>

                <!-- Custom Message Section -->
                <div class="custom-message-section">
                    <h4>رسالة إضافية (اختيارية)</h4>
                    <textarea id="custom-message"
                              placeholder="أضف تفاصيل إضافية عن الحالة الطارئة..."
                              maxlength="500"
                              rows="4"></textarea>
                    <div class="message-counter">
                        <span id="message-count">0</span>/500 حرف
                    </div>

                    <!-- Quick Message Templates -->
                    <div class="quick-templates">
                        <h5>رسائل سريعة:</h5>
                        <div class="template-buttons">
                            <button type="button" class="template-btn" data-template="حالة عاجلة جداً - تدخل فوري مطلوب">
                                حالة عاجلة
                            </button>
                            <button type="button" class="template-btn" data-template="عدد كبير من الأشخاص متأثرين">
                                عدد كبير متأثر
                            </button>
                            <button type="button" class="template-btn" data-template="الوضع تحت السيطرة حالياً">
                                تحت السيطرة
                            </button>
                            <button type="button" class="template-btn" data-template="نحتاج مساعدة إضافية">
                                نحتاج مساعدة
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Sender Information -->
                <div class="sender-info-section">
                    <h4>معلومات المرسل</h4>
                    <div class="sender-details" id="sender-details">
                        <!-- Will be populated by JavaScript -->
                    </div>
                </div>

                <div class="modal-actions">
                    <button id="cancel-alert" class="btn-secondary">إلغاء</button>
                    <button id="confirm-alert" class="btn-primary">إرسال الإنذار</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Success Modal -->
    <div id="success-modal" class="modal hidden">
        <div class="modal-content success">
            <div class="success-icon">✅</div>
            <h3>تم إرسال الإنذار بنجاح</h3>
            <p>تم إشعار جميع المسؤولين والمستخدمين في المنطقة</p>
            <button id="close-success" class="btn-primary">حسناً</button>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/database.js"></script>
    <script src="js/location.js"></script>
    <script src="js/alerts.js"></script>
    <script src="js/notifications.js"></script>
    <script src="js/maps.js"></script>
    <script src="js/websocket.js"></script>
    <script src="js/sms-service.js"></script>
    <script src="js/enhanced-notifications.js"></script>
    <script src="js/app.js"></script>

    <script>
        // Check authentication before loading app
        document.addEventListener('DOMContentLoaded', () => {
            const currentUser = localStorage.getItem('currentUser');
            if (!currentUser) {
                // Redirect to authentication page
                window.location.href = 'auth.html';
                return;
            }

            // Check if session is still valid
            const user = JSON.parse(currentUser);
            const lastLogin = new Date(user.lastLogin || 0);
            const now = new Date();
            const daysDiff = (now - lastLogin) / (1000 * 60 * 60 * 24);

            if (daysDiff > 30) {
                // Session expired, redirect to auth
                localStorage.removeItem('currentUser');
                window.location.href = 'auth.html';
                return;
            }

            // Update last activity
            user.lastActivity = new Date().toISOString();
            localStorage.setItem('currentUser', JSON.stringify(user));
        });
    </script>
</body>
</html>
