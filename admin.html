<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المسؤولين - درع الطوارئ</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="admin-styles.css">
    <meta name="robots" content="noindex, nofollow">
</head>
<body class="admin-body">
    <!-- Login Screen -->
    <div id="login-screen" class="login-screen">
        <div class="login-container">
            <div class="login-header">
                <div class="shield-icon">🛡️</div>
                <h1>لوحة تحكم المسؤولين</h1>
                <p>درع الطوارئ</p>
            </div>
            <form id="login-form" class="login-form">
                <div class="form-group">
                    <label for="username">اسم المستخدم</label>
                    <input type="text" id="username" name="username" required>
                </div>
                <div class="form-group">
                    <label for="password">كلمة المرور</label>
                    <input type="password" id="password" name="password" required>
                </div>
                <button type="submit" class="login-btn">تسجيل الدخول</button>
                <div id="login-error" class="login-error hidden"></div>
            </form>
        </div>
    </div>

    <!-- Admin Dashboard -->
    <div id="admin-dashboard" class="admin-dashboard hidden">
        <!-- Header -->
        <header class="admin-header">
            <div class="admin-header-content">
                <div class="admin-logo">
                    <span class="shield-icon">🛡️</span>
                    <h1>لوحة تحكم المسؤولين</h1>
                </div>
                <div class="admin-actions">
                    <button id="refresh-data" class="action-btn">
                        <span>🔄</span>
                        تحديث البيانات
                    </button>
                    <button id="logout-btn" class="action-btn">
                        <span>🚪</span>
                        تسجيل الخروج
                    </button>
                </div>
            </div>
        </header>

        <!-- Navigation -->
        <nav class="admin-nav">
            <button class="nav-btn active" data-section="dashboard">
                <span>📊</span>
                لوحة المعلومات
            </button>
            <button class="nav-btn" data-section="alerts">
                <span>🚨</span>
                الإنذارات
            </button>
            <button class="nav-btn" data-section="map">
                <span>🗺️</span>
                الخريطة
            </button>
            <button class="nav-btn" data-section="users">
                <span>👥</span>
                المستخدمين
            </button>
            <button class="nav-btn" data-section="settings">
                <span>⚙️</span>
                الإعدادات
            </button>
        </nav>

        <!-- Main Content -->
        <main class="admin-main">
            <!-- Dashboard Section -->
            <section id="dashboard-section" class="admin-section active">
                <div class="section-header">
                    <h2>لوحة المعلومات</h2>
                    <div class="last-update">
                        آخر تحديث: <span id="last-update-time">--</span>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="stats-grid">
                    <div class="stat-card critical">
                        <div class="stat-icon">🚨</div>
                        <div class="stat-content">
                            <h3>الإنذارات النشطة</h3>
                            <div class="stat-number" id="active-alerts">0</div>
                        </div>
                    </div>
                    <div class="stat-card warning">
                        <div class="stat-icon">📊</div>
                        <div class="stat-content">
                            <h3>إجمالي اليوم</h3>
                            <div class="stat-number" id="today-alerts">0</div>
                        </div>
                    </div>
                    <div class="stat-card info">
                        <div class="stat-icon">👥</div>
                        <div class="stat-content">
                            <h3>المستخدمين المتصلين</h3>
                            <div class="stat-number" id="online-users">0</div>
                        </div>
                    </div>
                    <div class="stat-card success">
                        <div class="stat-icon">✅</div>
                        <div class="stat-content">
                            <h3>الإنذارات المحلولة</h3>
                            <div class="stat-number" id="resolved-alerts">0</div>
                        </div>
                    </div>
                </div>

                <!-- Recent Alerts -->
                <div class="dashboard-section">
                    <h3>الإنذارات الأخيرة</h3>
                    <div id="recent-alerts-list" class="alerts-list">
                        <!-- Recent alerts will be populated here -->
                    </div>
                </div>

                <!-- Alert Types Chart -->
                <div class="dashboard-section">
                    <h3>توزيع أنواع الإنذارات</h3>
                    <div id="alert-types-chart" class="chart-container">
                        <!-- Chart will be rendered here -->
                    </div>
                </div>
            </section>

            <!-- Alerts Section -->
            <section id="alerts-section" class="admin-section">
                <div class="section-header">
                    <h2>إدارة الإنذارات</h2>
                    <div class="section-actions">
                        <select id="alert-filter" class="filter-select">
                            <option value="all">جميع الإنذارات</option>
                            <option value="active">النشطة</option>
                            <option value="resolved">المحلولة</option>
                            <option value="cancelled">الملغاة</option>
                        </select>
                        <select id="alert-type-filter" class="filter-select">
                            <option value="all">جميع الأنواع</option>
                            <option value="ambulance">إسعاف</option>
                            <option value="fire">حريق</option>
                            <option value="civil-defense">دفاع مدني</option>
                            <option value="settler-attack">هجوم مستوطنين</option>
                            <option value="settlers-present">مستوطنين في الموقع</option>
                            <option value="intrusion">اقتحام</option>
                        </select>
                    </div>
                </div>

                <div id="alerts-table-container" class="table-container">
                    <table id="alerts-table" class="admin-table">
                        <thead>
                            <tr>
                                <th>النوع</th>
                                <th>الموقع</th>
                                <th>الوقت</th>
                                <th>الحالة</th>
                                <th>المستخدم</th>
                                <th>الرسالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="alerts-table-body">
                            <!-- Alerts will be populated here -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Map Section -->
            <section id="map-section" class="admin-section">
                <div class="section-header">
                    <h2>خريطة الإنذارات</h2>
                    <div class="map-controls">
                        <button id="center-map" class="action-btn">
                            <span>🎯</span>
                            توسيط الخريطة
                        </button>
                        <button id="toggle-heatmap" class="action-btn">
                            <span>🔥</span>
                            خريطة الحرارة
                        </button>
                    </div>
                </div>
                <div id="admin-map" class="admin-map">
                    <div class="map-placeholder">
                        <div class="map-icon">🗺️</div>
                        <p>سيتم إضافة الخريطة التفاعلية قريباً</p>
                        <p>ستعرض مواقع الإنذارات في الوقت الفعلي</p>
                    </div>
                </div>
            </section>

            <!-- Users Section -->
            <section id="users-section" class="admin-section">
                <div class="section-header">
                    <h2>إدارة المستخدمين</h2>
                    <button id="add-user" class="action-btn primary">
                        <span>➕</span>
                        إضافة مستخدم
                    </button>
                </div>

                <div id="users-table-container" class="table-container">
                    <table id="users-table" class="admin-table">
                        <thead>
                            <tr>
                                <th>المستخدم</th>
                                <th>النوع</th>
                                <th>آخر نشاط</th>
                                <th>الحالة</th>
                                <th>عدد الإنذارات</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="users-table-body">
                            <!-- Users will be populated here -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Settings Section -->
            <section id="settings-section" class="admin-section">
                <div class="section-header">
                    <h2>إعدادات النظام</h2>
                </div>

                <div class="settings-grid">
                    <div class="settings-card">
                        <h3>إعدادات الإشعارات</h3>
                        <div class="setting-item">
                            <label class="setting-label">
                                <input type="checkbox" id="auto-notifications" checked>
                                <span class="checkmark"></span>
                                إشعارات تلقائية للمسؤولين
                            </label>
                        </div>
                        <div class="setting-item">
                            <label class="setting-label">
                                <input type="checkbox" id="sound-alerts" checked>
                                <span class="checkmark"></span>
                                تنبيهات صوتية
                            </label>
                        </div>
                        <div class="setting-item">
                            <label for="alert-radius">نطاق الإنذار (متر)</label>
                            <input type="number" id="alert-radius" value="5000" min="100" max="50000">
                        </div>
                    </div>

                    <div class="settings-card">
                        <h3>إعدادات الأمان</h3>
                        <div class="setting-item">
                            <label class="setting-label">
                                <input type="checkbox" id="require-confirmation" checked>
                                <span class="checkmark"></span>
                                تأكيد الإنذارات قبل الإرسال
                            </label>
                        </div>
                        <div class="setting-item">
                            <label class="setting-label">
                                <input type="checkbox" id="log-activities" checked>
                                <span class="checkmark"></span>
                                تسجيل جميع الأنشطة
                            </label>
                        </div>
                        <div class="setting-item">
                            <button id="export-data" class="action-btn">
                                <span>📥</span>
                                تصدير البيانات
                            </button>
                        </div>
                    </div>

                    <div class="settings-card">
                        <h3>إعدادات النظام</h3>
                        <div class="setting-item">
                            <label for="refresh-interval">فترة التحديث (ثانية)</label>
                            <input type="number" id="refresh-interval" value="30" min="5" max="300">
                        </div>
                        <div class="setting-item">
                            <label for="max-alerts">الحد الأقصى للإنذارات المحفوظة</label>
                            <input type="number" id="max-alerts" value="1000" min="100" max="10000">
                        </div>
                        <div class="setting-item">
                            <button id="clear-old-data" class="action-btn warning">
                                <span>🗑️</span>
                                مسح البيانات القديمة
                            </button>
                        </div>
                    </div>
                </div>

                <div class="settings-actions">
                    <button id="save-settings" class="action-btn primary">
                        <span>💾</span>
                        حفظ الإعدادات
                    </button>
                    <button id="reset-settings" class="action-btn secondary">
                        <span>🔄</span>
                        إعادة تعيين
                    </button>
                </div>
            </section>
        </main>
    </div>

    <!-- Modals -->
    <div id="alert-details-modal" class="modal hidden">
        <div class="modal-content large">
            <div class="modal-header">
                <h3>تفاصيل الإنذار</h3>
                <button class="close-btn">✕</button>
            </div>
            <div class="modal-body" id="alert-details-content">
                <!-- Alert details will be populated here -->
            </div>
        </div>
    </div>

    <div id="user-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="user-modal-title">إضافة مستخدم</h3>
                <button class="close-btn">✕</button>
            </div>
            <div class="modal-body">
                <form id="user-form">
                    <div class="form-group">
                        <label for="user-name">اسم المستخدم</label>
                        <input type="text" id="user-name" required>
                    </div>
                    <div class="form-group">
                        <label for="user-email">البريد الإلكتروني</label>
                        <input type="email" id="user-email" required>
                    </div>
                    <div class="form-group">
                        <label for="user-role">النوع</label>
                        <select id="user-role" required>
                            <option value="user">مستخدم عادي</option>
                            <option value="admin">مسؤول</option>
                            <option value="moderator">مشرف</option>
                        </select>
                    </div>
                    <div class="modal-actions">
                        <button type="button" class="btn-secondary" id="cancel-user">إلغاء</button>
                        <button type="submit" class="btn-primary">حفظ</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/admin.js"></script>
    <script src="js/alerts.js"></script>
    <script src="js/notifications.js"></script>
</body>
</html>
