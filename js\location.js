// Location Management Module
class LocationManager {
    constructor() {
        this.currentLocation = null;
        this.watchId = null;
        this.locationHistory = [];
        this.accuracy = null;
    }

    // Get current position with high accuracy
    async getCurrentPosition(options = {}) {
        const defaultOptions = {
            enableHighAccuracy: true,
            timeout: 15000,
            maximumAge: 300000 // 5 minutes
        };

        const finalOptions = { ...defaultOptions, ...options };

        return new Promise((resolve, reject) => {
            if (!navigator.geolocation) {
                reject(new Error('Geolocation is not supported by this browser'));
                return;
            }

            navigator.geolocation.getCurrentPosition(
                (position) => {
                    this.currentLocation = {
                        latitude: position.coords.latitude,
                        longitude: position.coords.longitude,
                        accuracy: position.coords.accuracy,
                        timestamp: new Date(position.timestamp)
                    };
                    
                    this.accuracy = position.coords.accuracy;
                    this.addToHistory(this.currentLocation);
                    resolve(this.currentLocation);
                },
                (error) => {
                    console.error('Geolocation error:', error);
                    reject(this.handleLocationError(error));
                },
                finalOptions
            );
        });
    }

    // Start watching position changes
    startWatching(callback, options = {}) {
        if (!navigator.geolocation) {
            throw new Error('Geolocation is not supported');
        }

        const defaultOptions = {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 60000 // 1 minute
        };

        const finalOptions = { ...defaultOptions, ...options };

        this.watchId = navigator.geolocation.watchPosition(
            (position) => {
                const newLocation = {
                    latitude: position.coords.latitude,
                    longitude: position.coords.longitude,
                    accuracy: position.coords.accuracy,
                    timestamp: new Date(position.timestamp)
                };

                // Only update if location changed significantly
                if (this.hasLocationChanged(newLocation)) {
                    this.currentLocation = newLocation;
                    this.accuracy = position.coords.accuracy;
                    this.addToHistory(newLocation);
                    
                    if (callback) {
                        callback(newLocation);
                    }
                }
            },
            (error) => {
                console.error('Watch position error:', error);
                if (callback) {
                    callback(null, this.handleLocationError(error));
                }
            },
            finalOptions
        );

        return this.watchId;
    }

    // Stop watching position
    stopWatching() {
        if (this.watchId !== null) {
            navigator.geolocation.clearWatch(this.watchId);
            this.watchId = null;
        }
    }

    // Check if location changed significantly (more than 10 meters)
    hasLocationChanged(newLocation) {
        if (!this.currentLocation) return true;

        const distance = this.calculateDistance(
            this.currentLocation.latitude,
            this.currentLocation.longitude,
            newLocation.latitude,
            newLocation.longitude
        );

        return distance > 10; // 10 meters threshold
    }

    // Calculate distance between two points in meters
    calculateDistance(lat1, lon1, lat2, lon2) {
        const R = 6371e3; // Earth's radius in meters
        const φ1 = lat1 * Math.PI / 180;
        const φ2 = lat2 * Math.PI / 180;
        const Δφ = (lat2 - lat1) * Math.PI / 180;
        const Δλ = (lon2 - lon1) * Math.PI / 180;

        const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
                  Math.cos(φ1) * Math.cos(φ2) *
                  Math.sin(Δλ/2) * Math.sin(Δλ/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

        return R * c;
    }

    // Add location to history
    addToHistory(location) {
        this.locationHistory.unshift(location);
        
        // Keep only last 100 locations
        if (this.locationHistory.length > 100) {
            this.locationHistory = this.locationHistory.slice(0, 100);
        }

        // Save to localStorage
        this.saveLocationHistory();
    }

    // Save location history to localStorage
    saveLocationHistory() {
        try {
            localStorage.setItem('locationHistory', JSON.stringify(this.locationHistory));
        } catch (error) {
            console.error('Failed to save location history:', error);
        }
    }

    // Load location history from localStorage
    loadLocationHistory() {
        try {
            const stored = localStorage.getItem('locationHistory');
            if (stored) {
                this.locationHistory = JSON.parse(stored);
                if (this.locationHistory.length > 0) {
                    this.currentLocation = this.locationHistory[0];
                }
            }
        } catch (error) {
            console.error('Failed to load location history:', error);
            this.locationHistory = [];
        }
    }

    // Handle geolocation errors
    handleLocationError(error) {
        let message = 'حدث خطأ في تحديد الموقع';
        
        switch (error.code) {
            case error.PERMISSION_DENIED:
                message = 'تم رفض الإذن للوصول إلى الموقع';
                break;
            case error.POSITION_UNAVAILABLE:
                message = 'معلومات الموقع غير متاحة';
                break;
            case error.TIMEOUT:
                message = 'انتهت مهلة طلب تحديد الموقع';
                break;
            default:
                message = 'حدث خطأ غير معروف في تحديد الموقع';
                break;
        }

        return new Error(message);
    }

    // Get location accuracy status
    getAccuracyStatus() {
        if (!this.accuracy) return 'غير معروف';
        
        if (this.accuracy <= 10) return 'ممتاز';
        if (this.accuracy <= 50) return 'جيد';
        if (this.accuracy <= 100) return 'متوسط';
        return 'ضعيف';
    }

    // Format location for display
    formatLocation(location = this.currentLocation) {
        if (!location) return 'الموقع غير محدد';

        return {
            coordinates: `${location.latitude.toFixed(6)}, ${location.longitude.toFixed(6)}`,
            accuracy: `دقة: ${Math.round(location.accuracy)} متر`,
            timestamp: location.timestamp ? location.timestamp.toLocaleString('ar-SA') : '',
            status: this.getAccuracyStatus()
        };
    }

    // Get address from coordinates (reverse geocoding)
    async getAddressFromCoordinates(lat, lon) {
        try {
            // Using OpenStreetMap Nominatim API (free)
            const response = await fetch(
                `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lon}&accept-language=ar`
            );
            
            if (!response.ok) {
                throw new Error('Failed to fetch address');
            }

            const data = await response.json();
            return data.display_name || 'العنوان غير متاح';
        } catch (error) {
            console.error('Reverse geocoding error:', error);
            return 'العنوان غير متاح';
        }
    }

    // Manual location input
    setManualLocation(latitude, longitude) {
        if (isNaN(latitude) || isNaN(longitude)) {
            throw new Error('إحداثيات غير صحيحة');
        }

        if (latitude < -90 || latitude > 90) {
            throw new Error('خط العرض يجب أن يكون بين -90 و 90');
        }

        if (longitude < -180 || longitude > 180) {
            throw new Error('خط الطول يجب أن يكون بين -180 و 180');
        }

        this.currentLocation = {
            latitude: parseFloat(latitude),
            longitude: parseFloat(longitude),
            accuracy: 0, // Manual input assumed to be exact
            timestamp: new Date(),
            manual: true
        };

        this.addToHistory(this.currentLocation);
        return this.currentLocation;
    }

    // Check if location services are available
    isLocationAvailable() {
        return 'geolocation' in navigator;
    }

    // Get location permission status
    async getLocationPermission() {
        if (!navigator.permissions) {
            return 'unknown';
        }

        try {
            const permission = await navigator.permissions.query({ name: 'geolocation' });
            return permission.state; // 'granted', 'denied', or 'prompt'
        } catch (error) {
            console.error('Permission query error:', error);
            return 'unknown';
        }
    }

    // Request location permission
    async requestLocationPermission() {
        try {
            const position = await this.getCurrentPosition({ timeout: 5000 });
            return 'granted';
        } catch (error) {
            if (error.message.includes('رفض الإذن')) {
                return 'denied';
            }
            return 'error';
        }
    }

    // Export location data
    exportLocationData() {
        return {
            currentLocation: this.currentLocation,
            locationHistory: this.locationHistory,
            accuracy: this.accuracy,
            timestamp: new Date().toISOString()
        };
    }

    // Import location data
    importLocationData(data) {
        if (data.currentLocation) {
            this.currentLocation = data.currentLocation;
        }
        if (data.locationHistory) {
            this.locationHistory = data.locationHistory;
        }
        if (data.accuracy) {
            this.accuracy = data.accuracy;
        }
    }
}

// Create global instance
window.locationManager = new LocationManager();

// Load location history on initialization
document.addEventListener('DOMContentLoaded', () => {
    window.locationManager.loadLocationHistory();
});
