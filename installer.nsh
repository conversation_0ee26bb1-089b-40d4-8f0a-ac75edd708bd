; Emergency Shield NSIS Installer Script
; درع الطوارئ - سكريبت المثبت

!include "MUI2.nsh"
!include "FileFunc.nsh"

; Installer settings
!define PRODUCT_NAME "درع الطوارئ - Emergency Shield"
!define PRODUCT_VERSION "2.0.0"
!define PRODUCT_PUBLISHER "Emergency Shield Team"
!define PRODUCT_WEB_SITE "https://emergency-shield.local"
!define PRODUCT_DIR_REGKEY "Software\Microsoft\Windows\CurrentVersion\App Paths\emergency-shield.exe"
!define PRODUCT_UNINST_KEY "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}"

; M<PERSON> Settings
!define MUI_ABORTWARNING
!define MUI_ICON "icons\icon.ico"
!define MUI_UNICON "icons\icon.ico"
!define MUI_WELCOMEFINISHPAGE_BITMAP "icons\installer-banner.bmp"
!define MUI_UNWELCOMEFINISHPAGE_BITMAP "icons\installer-banner.bmp"

; Language Selection Dialog Settings
!define MUI_LANGDLL_REGISTRY_ROOT "HKCU"
!define MUI_LANGDLL_REGISTRY_KEY "Software\${PRODUCT_NAME}"
!define MUI_LANGDLL_REGISTRY_VALUENAME "Installer Language"

; Pages
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "LICENSE.txt"
!insertmacro MUI_PAGE_COMPONENTS
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

; Languages
!insertmacro MUI_LANGUAGE "Arabic"
!insertmacro MUI_LANGUAGE "English"

; Reserve files
!insertmacro MUI_RESERVEFILE_LANGDLL

; Installer sections
Section "التطبيق الأساسي" SEC01
  SectionIn RO
  SetOutPath "$INSTDIR"
  SetOverwrite ifnewer
  
  ; Main application files
  File "emergency-shield.exe"
  File "*.dll"
  File "*.pak"
  File "*.bin"
  File "*.dat"
  File "*.json"
  File "*.html"
  File "*.css"
  File "*.js"
  
  ; Resources
  SetOutPath "$INSTDIR\icons"
  File /r "icons\*.*"
  
  SetOutPath "$INSTDIR\js"
  File /r "js\*.*"
  
  ; Create shortcuts
  CreateDirectory "$SMPROGRAMS\${PRODUCT_NAME}"
  CreateShortCut "$SMPROGRAMS\${PRODUCT_NAME}\${PRODUCT_NAME}.lnk" "$INSTDIR\emergency-shield.exe"
  CreateShortCut "$SMPROGRAMS\${PRODUCT_NAME}\إلغاء التثبيت.lnk" "$INSTDIR\uninst.exe"
  CreateShortCut "$DESKTOP\${PRODUCT_NAME}.lnk" "$INSTDIR\emergency-shield.exe"
  
  ; Registry entries
  WriteRegStr HKLM "${PRODUCT_DIR_REGKEY}" "" "$INSTDIR\emergency-shield.exe"
  WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "DisplayName" "$(^Name)"
  WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "UninstallString" "$INSTDIR\uninst.exe"
  WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "DisplayIcon" "$INSTDIR\emergency-shield.exe"
  WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "DisplayVersion" "${PRODUCT_VERSION}"
  WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "URLInfoAbout" "${PRODUCT_WEB_SITE}"
  WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "Publisher" "${PRODUCT_PUBLISHER}"
  WriteRegDWORD HKLM "${PRODUCT_UNINST_KEY}" "NoModify" 1
  WriteRegDWORD HKLM "${PRODUCT_UNINST_KEY}" "NoRepair" 1
SectionEnd

Section "التشغيل التلقائي" SEC02
  WriteRegStr HKCU "Software\Microsoft\Windows\CurrentVersion\Run" "${PRODUCT_NAME}" "$INSTDIR\emergency-shield.exe --minimized"
SectionEnd

Section "اختصارات إضافية" SEC03
  CreateShortCut "$QUICKLAUNCH\${PRODUCT_NAME}.lnk" "$INSTDIR\emergency-shield.exe"
SectionEnd

; Section descriptions
!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC01} "الملفات الأساسية للتطبيق (مطلوبة)"
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC02} "تشغيل التطبيق تلقائياً عند بدء تشغيل Windows"
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC03} "إنشاء اختصارات إضافية في شريط المهام السريع"
!insertmacro MUI_FUNCTION_DESCRIPTION_END

; Uninstaller section
Section Uninstall
  ; Remove registry keys
  DeleteRegKey HKLM "${PRODUCT_UNINST_KEY}"
  DeleteRegKey HKLM "${PRODUCT_DIR_REGKEY}"
  DeleteRegValue HKCU "Software\Microsoft\Windows\CurrentVersion\Run" "${PRODUCT_NAME}"
  
  ; Remove files and directories
  Delete "$INSTDIR\emergency-shield.exe"
  Delete "$INSTDIR\*.dll"
  Delete "$INSTDIR\*.pak"
  Delete "$INSTDIR\*.bin"
  Delete "$INSTDIR\*.dat"
  Delete "$INSTDIR\*.json"
  Delete "$INSTDIR\*.html"
  Delete "$INSTDIR\*.css"
  Delete "$INSTDIR\*.js"
  Delete "$INSTDIR\uninst.exe"
  
  RMDir /r "$INSTDIR\icons"
  RMDir /r "$INSTDIR\js"
  RMDir "$INSTDIR"
  
  ; Remove shortcuts
  Delete "$SMPROGRAMS\${PRODUCT_NAME}\*.*"
  RMDir "$SMPROGRAMS\${PRODUCT_NAME}"
  Delete "$DESKTOP\${PRODUCT_NAME}.lnk"
  Delete "$QUICKLAUNCH\${PRODUCT_NAME}.lnk"
  
  SetAutoClose true
SectionEnd

; Installer functions
Function .onInit
  !insertmacro MUI_LANGDLL_DISPLAY
FunctionEnd

Function un.onInit
  !insertmacro MUI_UNGETLANGUAGE
FunctionEnd

Function .onInstSuccess
  ; Show completion message
  MessageBox MB_ICONINFORMATION "تم تثبيت ${PRODUCT_NAME} بنجاح!$\n$\nيمكنك الآن تشغيل التطبيق من قائمة ابدأ أو من سطح المكتب."
FunctionEnd
