const { app, BrowserWindow, Menu, ipc<PERSON>ain, dialog, shell, Tray, nativeImage } = require('electron');
const path = require('path');
const { autoUpdater } = require('electron-updater');
const log = require('electron-log');
const Store = require('electron-store');

// Configure logging
log.transports.file.level = 'info';
autoUpdater.logger = log;

// Initialize store for app settings
const store = new Store();

// Keep a global reference of the window object
let mainWindow;
let adminWindow;
let tray;
let isQuitting = false;

// App configuration
const APP_CONFIG = {
    name: 'درع الطوارئ - Emergency Shield',
    version: app.getVersion(),
    isDev: process.env.NODE_ENV === 'development' || process.argv.includes('--dev'),
    minWidth: 1024,
    minHeight: 768,
    defaultWidth: 1200,
    defaultHeight: 900
};

function createMainWindow() {
    // Get saved window bounds or use defaults
    const windowBounds = store.get('windowBounds', {
        width: APP_CONFIG.defaultWidth,
        height: APP_CONFIG.defaultHeight
    });

    // Create the browser window
    mainWindow = new BrowserWindow({
        width: windowBounds.width,
        height: windowBounds.height,
        minWidth: APP_CONFIG.minWidth,
        minHeight: APP_CONFIG.minHeight,
        x: windowBounds.x,
        y: windowBounds.y,
        icon: path.join(__dirname, 'icons', 'icon.png'),
        title: APP_CONFIG.name,
        show: false, // Don't show until ready
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            enableRemoteModule: false,
            preload: path.join(__dirname, 'electron-preload.js'),
            webSecurity: !APP_CONFIG.isDev
        },
        titleBarStyle: 'default',
        autoHideMenuBar: false,
        backgroundColor: '#ffffff',
        vibrancy: 'under-window',
        visualEffectState: 'active'
    });

    // Load the app
    const startUrl = APP_CONFIG.isDev 
        ? 'http://localhost:8000' 
        : `file://${path.join(__dirname, 'index.html')}`;
    
    mainWindow.loadURL(startUrl);

    // Show window when ready
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        
        // Focus window
        if (APP_CONFIG.isDev) {
            mainWindow.webContents.openDevTools();
        }
    });

    // Save window bounds on resize/move
    mainWindow.on('resize', saveWindowBounds);
    mainWindow.on('move', saveWindowBounds);

    // Handle window closed
    mainWindow.on('closed', () => {
        mainWindow = null;
    });

    // Handle window close (minimize to tray)
    mainWindow.on('close', (event) => {
        if (!isQuitting && store.get('minimizeToTray', true)) {
            event.preventDefault();
            mainWindow.hide();
            
            // Show notification
            if (tray) {
                tray.displayBalloon({
                    iconType: 'info',
                    title: 'درع الطوارئ',
                    content: 'التطبيق يعمل في الخلفية. انقر على الأيقونة للعودة.'
                });
            }
        }
    });

    // Handle external links
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
    });

    return mainWindow;
}

function createAdminWindow() {
    if (adminWindow) {
        adminWindow.focus();
        return;
    }

    adminWindow = new BrowserWindow({
        width: 1400,
        height: 1000,
        minWidth: 1200,
        minHeight: 800,
        icon: path.join(__dirname, 'icons', 'icon.png'),
        title: 'لوحة تحكم المسؤولين - درع الطوارئ',
        parent: mainWindow,
        modal: false,
        show: false,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            enableRemoteModule: false,
            preload: path.join(__dirname, 'electron-preload.js'),
            webSecurity: !APP_CONFIG.isDev
        }
    });

    const adminUrl = APP_CONFIG.isDev 
        ? 'http://localhost:8000/admin.html' 
        : `file://${path.join(__dirname, 'admin.html')}`;
    
    adminWindow.loadURL(adminUrl);

    adminWindow.once('ready-to-show', () => {
        adminWindow.show();
    });

    adminWindow.on('closed', () => {
        adminWindow = null;
    });
}

function createTray() {
    const trayIcon = nativeImage.createFromPath(path.join(__dirname, 'icons', 'icon.png'));
    tray = new Tray(trayIcon.resize({ width: 16, height: 16 }));
    
    const contextMenu = Menu.buildFromTemplate([
        {
            label: 'إظهار التطبيق',
            click: () => {
                if (mainWindow) {
                    mainWindow.show();
                    mainWindow.focus();
                }
            }
        },
        {
            label: 'لوحة التحكم',
            click: () => createAdminWindow()
        },
        { type: 'separator' },
        {
            label: 'إعدادات',
            click: () => {
                // Open settings
                if (mainWindow) {
                    mainWindow.webContents.send('open-settings');
                }
            }
        },
        { type: 'separator' },
        {
            label: 'خروج',
            click: () => {
                isQuitting = true;
                app.quit();
            }
        }
    ]);

    tray.setToolTip('درع الطوارئ - Emergency Shield');
    tray.setContextMenu(contextMenu);

    // Double click to show/hide
    tray.on('double-click', () => {
        if (mainWindow) {
            if (mainWindow.isVisible()) {
                mainWindow.hide();
            } else {
                mainWindow.show();
                mainWindow.focus();
            }
        }
    });
}

function createMenu() {
    const template = [
        {
            label: 'ملف',
            submenu: [
                {
                    label: 'نافذة جديدة',
                    accelerator: 'CmdOrCtrl+N',
                    click: () => createMainWindow()
                },
                { type: 'separator' },
                {
                    label: 'لوحة التحكم',
                    accelerator: 'CmdOrCtrl+Shift+A',
                    click: () => createAdminWindow()
                },
                { type: 'separator' },
                {
                    label: 'خروج',
                    accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                    click: () => {
                        isQuitting = true;
                        app.quit();
                    }
                }
            ]
        },
        {
            label: 'تحرير',
            submenu: [
                { label: 'تراجع', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
                { label: 'إعادة', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
                { type: 'separator' },
                { label: 'قص', accelerator: 'CmdOrCtrl+X', role: 'cut' },
                { label: 'نسخ', accelerator: 'CmdOrCtrl+C', role: 'copy' },
                { label: 'لصق', accelerator: 'CmdOrCtrl+V', role: 'paste' }
            ]
        },
        {
            label: 'عرض',
            submenu: [
                { label: 'إعادة تحميل', accelerator: 'CmdOrCtrl+R', role: 'reload' },
                { label: 'إعادة تحميل قسري', accelerator: 'CmdOrCtrl+Shift+R', role: 'forceReload' },
                { label: 'أدوات المطور', accelerator: 'F12', role: 'toggleDevTools' },
                { type: 'separator' },
                { label: 'تكبير', accelerator: 'CmdOrCtrl+Plus', role: 'zoomIn' },
                { label: 'تصغير', accelerator: 'CmdOrCtrl+-', role: 'zoomOut' },
                { label: 'حجم طبيعي', accelerator: 'CmdOrCtrl+0', role: 'resetZoom' },
                { type: 'separator' },
                { label: 'ملء الشاشة', accelerator: 'F11', role: 'togglefullscreen' }
            ]
        },
        {
            label: 'نافذة',
            submenu: [
                { label: 'تصغير', accelerator: 'CmdOrCtrl+M', role: 'minimize' },
                { label: 'إغلاق', accelerator: 'CmdOrCtrl+W', role: 'close' }
            ]
        },
        {
            label: 'مساعدة',
            submenu: [
                {
                    label: 'حول التطبيق',
                    click: () => {
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: 'حول درع الطوارئ',
                            message: 'درع الطوارئ - Emergency Shield',
                            detail: `الإصدار: ${APP_CONFIG.version}\n\nنظام إنذار ذكي وفعال للطوارئ\n\nتم التطوير بواسطة فريق درع الطوارئ`,
                            buttons: ['موافق']
                        });
                    }
                },
                {
                    label: 'التحقق من التحديثات',
                    click: () => {
                        autoUpdater.checkForUpdatesAndNotify();
                    }
                }
            ]
        }
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
}

function saveWindowBounds() {
    if (mainWindow) {
        store.set('windowBounds', mainWindow.getBounds());
    }
}

// App event handlers
app.whenReady().then(() => {
    createMainWindow();
    createMenu();
    createTray();

    // Check for updates
    if (!APP_CONFIG.isDev) {
        autoUpdater.checkForUpdatesAndNotify();
    }

    app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
            createMainWindow();
        }
    });
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('before-quit', () => {
    isQuitting = true;
});

// IPC handlers
ipcMain.handle('get-app-version', () => {
    return app.getVersion();
});

ipcMain.handle('get-app-info', () => {
    return {
        name: APP_CONFIG.name,
        version: APP_CONFIG.version,
        isDev: APP_CONFIG.isDev
    };
});

ipcMain.handle('open-admin-panel', () => {
    createAdminWindow();
});

ipcMain.handle('show-notification', (event, options) => {
    if (mainWindow) {
        mainWindow.webContents.send('show-notification', options);
    }
});

ipcMain.handle('minimize-to-tray', () => {
    if (mainWindow) {
        mainWindow.hide();
    }
});

// Auto-updater events
autoUpdater.on('checking-for-update', () => {
    log.info('Checking for update...');
});

autoUpdater.on('update-available', (info) => {
    log.info('Update available.');
    dialog.showMessageBox(mainWindow, {
        type: 'info',
        title: 'تحديث متاح',
        message: 'يتوفر تحديث جديد للتطبيق',
        detail: 'سيتم تحميل التحديث في الخلفية وتثبيته عند إعادة تشغيل التطبيق.',
        buttons: ['موافق']
    });
});

autoUpdater.on('update-not-available', (info) => {
    log.info('Update not available.');
});

autoUpdater.on('error', (err) => {
    log.error('Error in auto-updater. ' + err);
});

autoUpdater.on('download-progress', (progressObj) => {
    let log_message = "Download speed: " + progressObj.bytesPerSecond;
    log_message = log_message + ' - Downloaded ' + progressObj.percent + '%';
    log_message = log_message + ' (' + progressObj.transferred + "/" + progressObj.total + ')';
    log.info(log_message);
});

autoUpdater.on('update-downloaded', (info) => {
    log.info('Update downloaded');
    dialog.showMessageBox(mainWindow, {
        type: 'info',
        title: 'تم تحميل التحديث',
        message: 'تم تحميل التحديث بنجاح',
        detail: 'سيتم تثبيت التحديث عند إعادة تشغيل التطبيق. هل تريد إعادة التشغيل الآن؟',
        buttons: ['إعادة التشغيل', 'لاحقاً']
    }).then((result) => {
        if (result.response === 0) {
            autoUpdater.quitAndInstall();
        }
    });
});
