<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار درع الطوارئ</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 2rem;
            color: white;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2rem;
            color: #333;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #1e3a8a;
        }
        
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            margin: 0.5rem;
            transition: background 0.2s;
        }
        
        .test-button:hover {
            background: #2563eb;
        }
        
        .test-result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 8px;
            font-family: monospace;
            font-size: 0.875rem;
        }
        
        .test-result.success {
            background: #f0fdf4;
            border: 1px solid #16a34a;
            color: #16a34a;
        }
        
        .test-result.error {
            background: #fef2f2;
            border: 1px solid #dc2626;
            color: #dc2626;
        }
        
        .test-result.info {
            background: #eff6ff;
            border: 1px solid #3b82f6;
            color: #3b82f6;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 0.5rem;
        }
        
        .status-indicator.success {
            background: #16a34a;
        }
        
        .status-indicator.error {
            background: #dc2626;
        }
        
        .status-indicator.warning {
            background: #f59e0b;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🛡️ اختبار درع الطوارئ</h1>
            <p>اختبار شامل لجميع وظائف التطبيق</p>
        </div>

        <div class="test-section">
            <h3>اختبار الوظائف الأساسية</h3>
            <button class="test-button" onclick="testBasicFunctions()">اختبار الوظائف الأساسية</button>
            <div id="basic-test-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>اختبار تحديد المواقع</h3>
            <button class="test-button" onclick="testLocation()">اختبار GPS</button>
            <button class="test-button" onclick="testManualLocation()">اختبار الموقع اليدوي</button>
            <div id="location-test-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>اختبار الإنذارات</h3>
            <button class="test-button" onclick="testAlerts()">اختبار إنشاء الإنذارات</button>
            <button class="test-button" onclick="testAlertStorage()">اختبار حفظ الإنذارات</button>
            <div id="alerts-test-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>اختبار الإشعارات</h3>
            <button class="test-button" onclick="testNotifications()">اختبار الإشعارات</button>
            <button class="test-button" onclick="testNotificationPermission()">اختبار أذونات الإشعارات</button>
            <div id="notifications-test-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>اختبار قاعدة البيانات</h3>
            <button class="test-button" onclick="testDatabase()">اختبار قاعدة البيانات</button>
            <button class="test-button" onclick="testEncryption()">اختبار التشفير</button>
            <div id="database-test-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>اختبار الاتصال</h3>
            <button class="test-button" onclick="testWebSocket()">اختبار WebSocket</button>
            <button class="test-button" onclick="testOfflineMode()">اختبار الوضع غير المتصل</button>
            <div id="connection-test-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>اختبار الأداء</h3>
            <button class="test-button" onclick="testPerformance()">اختبار الأداء</button>
            <button class="test-button" onclick="testMemoryUsage()">اختبار استخدام الذاكرة</button>
            <div id="performance-test-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>تقرير شامل</h3>
            <button class="test-button" onclick="runAllTests()">تشغيل جميع الاختبارات</button>
            <button class="test-button" onclick="generateReport()">إنشاء تقرير</button>
            <div id="full-test-result" class="test-result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // Test Results Storage
        let testResults = {};

        // Basic Functions Test
        async function testBasicFunctions() {
            const resultDiv = document.getElementById('basic-test-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result info';
            resultDiv.innerHTML = 'جاري اختبار الوظائف الأساسية...';

            try {
                const tests = [
                    { name: 'تحميل JavaScript', test: () => typeof window !== 'undefined' },
                    { name: 'LocalStorage', test: () => typeof localStorage !== 'undefined' },
                    { name: 'IndexedDB', test: () => typeof indexedDB !== 'undefined' },
                    { name: 'Geolocation API', test: () => 'geolocation' in navigator },
                    { name: 'Notification API', test: () => 'Notification' in window },
                    { name: 'Service Worker', test: () => 'serviceWorker' in navigator }
                ];

                let results = [];
                for (const test of tests) {
                    try {
                        const passed = test.test();
                        results.push(`✅ ${test.name}: ${passed ? 'متاح' : 'غير متاح'}`);
                    } catch (error) {
                        results.push(`❌ ${test.name}: خطأ - ${error.message}`);
                    }
                }

                testResults.basic = results;
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = results.join('<br>');
            } catch (error) {
                testResults.basic = [`❌ خطأ في الاختبار: ${error.message}`];
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `خطأ: ${error.message}`;
            }
        }

        // Location Test
        async function testLocation() {
            const resultDiv = document.getElementById('location-test-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result info';
            resultDiv.innerHTML = 'جاري اختبار تحديد الموقع...';

            try {
                if (!navigator.geolocation) {
                    throw new Error('Geolocation غير مدعوم');
                }

                const position = await new Promise((resolve, reject) => {
                    navigator.geolocation.getCurrentPosition(resolve, reject, {
                        timeout: 10000,
                        enableHighAccuracy: true
                    });
                });

                const result = `
                    ✅ تم تحديد الموقع بنجاح<br>
                    📍 خط العرض: ${position.coords.latitude.toFixed(6)}<br>
                    📍 خط الطول: ${position.coords.longitude.toFixed(6)}<br>
                    🎯 الدقة: ${Math.round(position.coords.accuracy)} متر
                `;

                testResults.location = [result];
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = result;
            } catch (error) {
                const errorMsg = `❌ فشل في تحديد الموقع: ${error.message}`;
                testResults.location = [errorMsg];
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = errorMsg;
            }
        }

        // Manual Location Test
        function testManualLocation() {
            const resultDiv = document.getElementById('location-test-result');
            resultDiv.style.display = 'block';
            
            const lat = 31.7683;
            const lon = 35.2137;
            
            const result = `
                ✅ اختبار الموقع اليدوي<br>
                📍 القدس: ${lat}, ${lon}<br>
                🎯 تم تعيين الموقع يدوياً
            `;
            
            testResults.manualLocation = [result];
            resultDiv.className = 'test-result success';
            resultDiv.innerHTML = result;
        }

        // Alerts Test
        async function testAlerts() {
            const resultDiv = document.getElementById('alerts-test-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result info';
            resultDiv.innerHTML = 'جاري اختبار الإنذارات...';

            try {
                const alertTypes = ['ambulance', 'fire', 'civil-defense', 'settler-attack'];
                let results = [];

                for (const type of alertTypes) {
                    const alert = {
                        id: `test_${Date.now()}_${type}`,
                        type: type,
                        location: { latitude: 31.7683, longitude: 35.2137 },
                        timestamp: new Date().toISOString(),
                        status: 'active'
                    };

                    // Test alert creation
                    results.push(`✅ إنذار ${type}: تم إنشاؤه بنجاح`);
                }

                testResults.alerts = results;
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = results.join('<br>');
            } catch (error) {
                const errorMsg = `❌ خطأ في اختبار الإنذارات: ${error.message}`;
                testResults.alerts = [errorMsg];
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = errorMsg;
            }
        }

        // Alert Storage Test
        async function testAlertStorage() {
            const resultDiv = document.getElementById('alerts-test-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result info';
            resultDiv.innerHTML = 'جاري اختبار حفظ الإنذارات...';

            try {
                const testAlert = {
                    id: `storage_test_${Date.now()}`,
                    type: 'test',
                    data: 'test data'
                };

                // Test localStorage
                localStorage.setItem('test_alert', JSON.stringify(testAlert));
                const retrieved = JSON.parse(localStorage.getItem('test_alert'));
                localStorage.removeItem('test_alert');

                const result = `
                    ✅ LocalStorage: يعمل بشكل صحيح<br>
                    ✅ حفظ البيانات: تم بنجاح<br>
                    ✅ استرجاع البيانات: تم بنجاح<br>
                    📊 حجم البيانات المختبرة: ${JSON.stringify(testAlert).length} بايت
                `;

                testResults.alertStorage = [result];
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = result;
            } catch (error) {
                const errorMsg = `❌ خطأ في اختبار التخزين: ${error.message}`;
                testResults.alertStorage = [errorMsg];
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = errorMsg;
            }
        }

        // Notifications Test
        async function testNotifications() {
            const resultDiv = document.getElementById('notifications-test-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result info';
            resultDiv.innerHTML = 'جاري اختبار الإشعارات...';

            try {
                if (!('Notification' in window)) {
                    throw new Error('الإشعارات غير مدعومة');
                }

                const permission = Notification.permission;
                let results = [`📋 حالة الأذونات: ${permission}`];

                if (permission === 'granted') {
                    const notification = new Notification('اختبار درع الطوارئ', {
                        body: 'هذا إشعار تجريبي',
                        icon: '/icons/icon-192x192.png',
                        dir: 'rtl',
                        lang: 'ar'
                    });

                    setTimeout(() => notification.close(), 3000);
                    results.push('✅ تم إرسال إشعار تجريبي');
                } else {
                    results.push('⚠️ الأذونات غير ممنوحة');
                }

                testResults.notifications = results;
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = results.join('<br>');
            } catch (error) {
                const errorMsg = `❌ خطأ في اختبار الإشعارات: ${error.message}`;
                testResults.notifications = [errorMsg];
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = errorMsg;
            }
        }

        // Notification Permission Test
        async function testNotificationPermission() {
            const resultDiv = document.getElementById('notifications-test-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result info';
            resultDiv.innerHTML = 'جاري طلب أذونات الإشعارات...';

            try {
                if (!('Notification' in window)) {
                    throw new Error('الإشعارات غير مدعومة');
                }

                const permission = await Notification.requestPermission();
                const result = `
                    ✅ طلب الأذونات: تم<br>
                    📋 النتيجة: ${permission}<br>
                    ${permission === 'granted' ? '✅ يمكن إرسال الإشعارات' : '⚠️ لا يمكن إرسال الإشعارات'}
                `;

                testResults.notificationPermission = [result];
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = result;
            } catch (error) {
                const errorMsg = `❌ خطأ في طلب الأذونات: ${error.message}`;
                testResults.notificationPermission = [errorMsg];
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = errorMsg;
            }
        }

        // Database Test
        async function testDatabase() {
            const resultDiv = document.getElementById('database-test-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result info';
            resultDiv.innerHTML = 'جاري اختبار قاعدة البيانات...';

            try {
                // Test IndexedDB
                const dbTest = await new Promise((resolve, reject) => {
                    const request = indexedDB.open('TestDB', 1);
                    request.onerror = () => reject(request.error);
                    request.onsuccess = () => {
                        const db = request.result;
                        db.close();
                        indexedDB.deleteDatabase('TestDB');
                        resolve(true);
                    };
                    request.onupgradeneeded = (event) => {
                        const db = event.target.result;
                        db.createObjectStore('test', { keyPath: 'id' });
                    };
                });

                const result = `
                    ✅ IndexedDB: متاح ويعمل<br>
                    ✅ إنشاء قاعدة البيانات: نجح<br>
                    ✅ حذف قاعدة البيانات: نجح<br>
                    📊 الحالة: جاهز للاستخدام
                `;

                testResults.database = [result];
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = result;
            } catch (error) {
                const errorMsg = `❌ خطأ في اختبار قاعدة البيانات: ${error.message}`;
                testResults.database = [errorMsg];
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = errorMsg;
            }
        }

        // Encryption Test
        function testEncryption() {
            const resultDiv = document.getElementById('database-test-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result info';
            resultDiv.innerHTML = 'جاري اختبار التشفير...';

            try {
                const testData = { sensitive: 'بيانات حساسة', number: 12345 };
                const encrypted = btoa(JSON.stringify(testData));
                const decrypted = JSON.parse(atob(encrypted));

                const result = `
                    ✅ التشفير: يعمل بشكل صحيح<br>
                    ✅ فك التشفير: يعمل بشكل صحيح<br>
                    📊 البيانات الأصلية: ${JSON.stringify(testData)}<br>
                    🔐 البيانات المشفرة: ${encrypted.substring(0, 50)}...<br>
                    ✅ التحقق: البيانات متطابقة
                `;

                testResults.encryption = [result];
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = result;
            } catch (error) {
                const errorMsg = `❌ خطأ في اختبار التشفير: ${error.message}`;
                testResults.encryption = [errorMsg];
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = errorMsg;
            }
        }

        // WebSocket Test
        function testWebSocket() {
            const resultDiv = document.getElementById('connection-test-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result info';
            resultDiv.innerHTML = 'جاري اختبار WebSocket...';

            try {
                const wsSupported = typeof WebSocket !== 'undefined';
                const result = `
                    ${wsSupported ? '✅' : '❌'} WebSocket API: ${wsSupported ? 'مدعوم' : 'غير مدعوم'}<br>
                    📡 الحالة: ${wsSupported ? 'جاهز للاتصال' : 'غير متاح'}<br>
                    ℹ️ ملاحظة: يتم محاكاة الاتصال في الوقت الفعلي محلياً
                `;

                testResults.websocket = [result];
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = result;
            } catch (error) {
                const errorMsg = `❌ خطأ في اختبار WebSocket: ${error.message}`;
                testResults.websocket = [errorMsg];
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = errorMsg;
            }
        }

        // Offline Mode Test
        function testOfflineMode() {
            const resultDiv = document.getElementById('connection-test-result');
            resultDiv.style.display = 'block';
            
            const isOnline = navigator.onLine;
            const swSupported = 'serviceWorker' in navigator;
            
            const result = `
                📡 حالة الاتصال: ${isOnline ? 'متصل' : 'غير متصل'}<br>
                ${swSupported ? '✅' : '❌'} Service Worker: ${swSupported ? 'مدعوم' : 'غير مدعوم'}<br>
                📱 الوضع غير المتصل: ${swSupported ? 'متاح' : 'غير متاح'}<br>
                💾 التخزين المحلي: متاح
            `;

            testResults.offline = [result];
            resultDiv.className = 'test-result success';
            resultDiv.innerHTML = result;
        }

        // Performance Test
        function testPerformance() {
            const resultDiv = document.getElementById('performance-test-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result info';
            resultDiv.innerHTML = 'جاري اختبار الأداء...';

            try {
                const start = performance.now();
                
                // Simulate some operations
                for (let i = 0; i < 10000; i++) {
                    const obj = { id: i, data: `test_${i}` };
                    JSON.stringify(obj);
                }
                
                const end = performance.now();
                const duration = end - start;

                const result = `
                    ⏱️ وقت المعالجة: ${duration.toFixed(2)} مللي ثانية<br>
                    🚀 الأداء: ${duration < 100 ? 'ممتاز' : duration < 500 ? 'جيد' : 'يحتاج تحسين'}<br>
                    💻 معلومات المتصفح: ${navigator.userAgent.split(' ')[0]}<br>
                    📊 الذاكرة: ${navigator.deviceMemory || 'غير معروف'} GB
                `;

                testResults.performance = [result];
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = result;
            } catch (error) {
                const errorMsg = `❌ خطأ في اختبار الأداء: ${error.message}`;
                testResults.performance = [errorMsg];
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = errorMsg;
            }
        }

        // Memory Usage Test
        async function testMemoryUsage() {
            const resultDiv = document.getElementById('performance-test-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result info';
            resultDiv.innerHTML = 'جاري اختبار استخدام الذاكرة...';

            try {
                let memoryInfo = 'غير متاح';
                
                if ('memory' in performance) {
                    const memory = performance.memory;
                    memoryInfo = `
                        📊 الذاكرة المستخدمة: ${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB<br>
                        📈 إجمالي الذاكرة: ${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB<br>
                        🔒 حد الذاكرة: ${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB
                    `;
                }

                let storageInfo = 'غير متاح';
                if ('storage' in navigator && 'estimate' in navigator.storage) {
                    const estimate = await navigator.storage.estimate();
                    storageInfo = `
                        💾 التخزين المستخدم: ${(estimate.usage / 1024 / 1024).toFixed(2)} MB<br>
                        📦 حد التخزين: ${(estimate.quota / 1024 / 1024).toFixed(2)} MB
                    `;
                }

                const result = `
                    🧠 معلومات الذاكرة:<br>
                    ${memoryInfo}<br>
                    💽 معلومات التخزين:<br>
                    ${storageInfo}
                `;

                testResults.memory = [result];
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = result;
            } catch (error) {
                const errorMsg = `❌ خطأ في اختبار الذاكرة: ${error.message}`;
                testResults.memory = [errorMsg];
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = errorMsg;
            }
        }

        // Run All Tests
        async function runAllTests() {
            const resultDiv = document.getElementById('full-test-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result info';
            resultDiv.innerHTML = 'جاري تشغيل جميع الاختبارات...';

            const tests = [
                testBasicFunctions,
                testLocation,
                testAlerts,
                testNotifications,
                testDatabase,
                testWebSocket,
                testPerformance
            ];

            for (const test of tests) {
                try {
                    await test();
                    await new Promise(resolve => setTimeout(resolve, 500)); // Small delay
                } catch (error) {
                    console.error('Test failed:', error);
                }
            }

            resultDiv.className = 'test-result success';
            resultDiv.innerHTML = '✅ تم تشغيل جميع الاختبارات. راجع النتائج أعلاه.';
        }

        // Generate Report
        function generateReport() {
            const resultDiv = document.getElementById('full-test-result');
            resultDiv.style.display = 'block';
            
            const report = {
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                results: testResults
            };

            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `emergency-shield-test-report-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);

            resultDiv.className = 'test-result success';
            resultDiv.innerHTML = '✅ تم إنشاء تقرير الاختبار وتحميله.';
        }
    </script>
</body>
</html>
