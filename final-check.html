<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص نهائي - درع الطوارئ</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            margin: 0;
            padding: 2rem;
            color: white;
            min-height: 100vh;
        }
        
        .check-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2rem;
            color: #333;
        }
        
        .check-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .check-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: #f8fafc;
            border-radius: 12px;
            border-left: 4px solid #10b981;
        }
        
        .check-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.75rem;
            margin: 0.5rem 0;
            background: white;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        
        .check-status {
            font-weight: 600;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
        }
        
        .status-pass {
            background: #f0fdf4;
            color: #16a34a;
            border: 1px solid #bbf7d0;
        }
        
        .status-fail {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        
        .status-warning {
            background: #fffbeb;
            color: #f59e0b;
            border: 1px solid #fed7aa;
        }
        
        .check-btn {
            background: #10b981;
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 12px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            margin: 0.5rem;
            transition: all 0.2s;
            display: block;
            width: 100%;
        }
        
        .check-btn:hover {
            background: #059669;
            transform: translateY(-2px);
        }
        
        .summary {
            text-align: center;
            padding: 2rem;
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
            border-radius: 12px;
            margin-top: 2rem;
        }
        
        .summary.success {
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
            border: 2px solid #16a34a;
        }
        
        .summary.warning {
            background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
            border: 2px solid #f59e0b;
        }
        
        .summary.error {
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            border: 2px solid #dc2626;
        }
    </style>
</head>
<body>
    <div class="check-container">
        <div class="check-header">
            <h1>🛡️ فحص نهائي شامل</h1>
            <p>التحقق من جميع وظائف درع الطوارئ</p>
        </div>

        <button class="check-btn" onclick="runFullCheck()">
            🔍 بدء الفحص الشامل
        </button>

        <div class="check-section">
            <h3>📁 فحص الملفات الأساسية</h3>
            <div id="files-check"></div>
        </div>

        <div class="check-section">
            <h3>🔧 فحص الوظائف البرمجية</h3>
            <div id="functions-check"></div>
        </div>

        <div class="check-section">
            <h3>🌐 فحص APIs والخدمات</h3>
            <div id="apis-check"></div>
        </div>

        <div class="check-section">
            <h3>💾 فحص التخزين والبيانات</h3>
            <div id="storage-check"></div>
        </div>

        <div class="check-section">
            <h3>🎨 فحص واجهة المستخدم</h3>
            <div id="ui-check"></div>
        </div>

        <div id="summary" class="summary" style="display: none;">
            <h2 id="summary-title"></h2>
            <p id="summary-text"></p>
            <div id="summary-details"></div>
        </div>
    </div>

    <!-- Load all scripts for testing -->
    <script src="fix-errors.js"></script>
    <script src="js/database.js"></script>
    <script src="js/location.js"></script>
    <script src="js/alerts.js"></script>
    <script src="js/notifications.js"></script>
    <script src="js/maps.js"></script>
    <script src="js/websocket.js"></script>
    <script src="js/sms-service.js"></script>
    <script src="js/enhanced-notifications.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/admin.js"></script>
    <script src="js/app.js"></script>

    <script>
        let checkResults = {
            files: 0,
            functions: 0,
            apis: 0,
            storage: 0,
            ui: 0,
            total: 0,
            passed: 0
        };

        function createCheckItem(name, status, details = '') {
            const item = document.createElement('div');
            item.className = 'check-item';
            
            const statusClass = status === 'pass' ? 'status-pass' : 
                               status === 'warning' ? 'status-warning' : 'status-fail';
            const statusText = status === 'pass' ? '✅ نجح' : 
                              status === 'warning' ? '⚠️ تحذير' : '❌ فشل';
            
            item.innerHTML = `
                <div>
                    <strong>${name}</strong>
                    ${details ? `<br><small style="color: #6b7280;">${details}</small>` : ''}
                </div>
                <span class="check-status ${statusClass}">${statusText}</span>
            `;
            
            checkResults.total++;
            if (status === 'pass') checkResults.passed++;
            
            return item;
        }

        function checkFiles() {
            const container = document.getElementById('files-check');
            container.innerHTML = '';
            
            const files = [
                { name: 'index.html', check: () => fetch('index.html').then(r => r.ok) },
                { name: 'admin.html', check: () => fetch('admin.html').then(r => r.ok) },
                { name: 'auth.html', check: () => fetch('auth.html').then(r => r.ok) },
                { name: 'styles.css', check: () => fetch('styles.css').then(r => r.ok) },
                { name: 'fix-errors.js', check: () => fetch('fix-errors.js').then(r => r.ok) }
            ];

            files.forEach(async (file) => {
                try {
                    const exists = await file.check();
                    const status = exists ? 'pass' : 'fail';
                    container.appendChild(createCheckItem(file.name, status));
                } catch (error) {
                    container.appendChild(createCheckItem(file.name, 'fail', error.message));
                }
            });
        }

        function checkFunctions() {
            const container = document.getElementById('functions-check');
            container.innerHTML = '';
            
            const functions = [
                { name: 'AlertManager', check: () => typeof AlertManager !== 'undefined' },
                { name: 'AuthManager', check: () => typeof AuthManager !== 'undefined' },
                { name: 'SMSService', check: () => typeof SMSService !== 'undefined' },
                { name: 'EnhancedNotificationManager', check: () => typeof EnhancedNotificationManager !== 'undefined' },
                { name: 'AdminDashboard', check: () => typeof AdminDashboard !== 'undefined' },
                { name: 'EmergencyApp', check: () => typeof EmergencyApp !== 'undefined' }
            ];

            functions.forEach(func => {
                try {
                    const exists = func.check();
                    const status = exists ? 'pass' : 'fail';
                    container.appendChild(createCheckItem(func.name, status));
                } catch (error) {
                    container.appendChild(createCheckItem(func.name, 'fail', error.message));
                }
            });
        }

        function checkAPIs() {
            const container = document.getElementById('apis-check');
            container.innerHTML = '';
            
            const apis = [
                { name: 'Geolocation API', check: () => 'geolocation' in navigator },
                { name: 'Notification API', check: () => 'Notification' in window },
                { name: 'Service Worker', check: () => 'serviceWorker' in navigator },
                { name: 'Local Storage', check: () => typeof localStorage !== 'undefined' },
                { name: 'IndexedDB', check: () => typeof indexedDB !== 'undefined' },
                { name: 'Fetch API', check: () => typeof fetch !== 'undefined' }
            ];

            apis.forEach(api => {
                try {
                    const supported = api.check();
                    const status = supported ? 'pass' : 'warning';
                    container.appendChild(createCheckItem(api.name, status));
                } catch (error) {
                    container.appendChild(createCheckItem(api.name, 'fail', error.message));
                }
            });
        }

        function checkStorage() {
            const container = document.getElementById('storage-check');
            container.innerHTML = '';
            
            const tests = [
                {
                    name: 'LocalStorage Write/Read',
                    check: () => {
                        localStorage.setItem('test', 'value');
                        const result = localStorage.getItem('test') === 'value';
                        localStorage.removeItem('test');
                        return result;
                    }
                },
                {
                    name: 'JSON Parsing',
                    check: () => {
                        const obj = { test: 'value' };
                        const json = JSON.stringify(obj);
                        const parsed = JSON.parse(json);
                        return parsed.test === 'value';
                    }
                },
                {
                    name: 'User Data Structure',
                    check: () => {
                        const user = {
                            fullName: 'Test User',
                            phoneNumber: '+970591234567',
                            userRole: 'citizen'
                        };
                        return user.fullName && user.phoneNumber && user.userRole;
                    }
                }
            ];

            tests.forEach(test => {
                try {
                    const result = test.check();
                    const status = result ? 'pass' : 'fail';
                    container.appendChild(createCheckItem(test.name, status));
                } catch (error) {
                    container.appendChild(createCheckItem(test.name, 'fail', error.message));
                }
            });
        }

        function checkUI() {
            const container = document.getElementById('ui-check');
            container.innerHTML = '';
            
            const elements = [
                { name: 'CSS Grid Support', check: () => CSS.supports('display', 'grid') },
                { name: 'CSS Flexbox Support', check: () => CSS.supports('display', 'flex') },
                { name: 'CSS Variables Support', check: () => CSS.supports('color', 'var(--test)') },
                { name: 'Touch Events', check: () => 'ontouchstart' in window },
                { name: 'Media Queries', check: () => window.matchMedia('(max-width: 768px)').matches !== undefined }
            ];

            elements.forEach(element => {
                try {
                    const supported = element.check();
                    const status = supported ? 'pass' : 'warning';
                    container.appendChild(createCheckItem(element.name, status));
                } catch (error) {
                    container.appendChild(createCheckItem(element.name, 'fail', error.message));
                }
            });
        }

        function showSummary() {
            const summary = document.getElementById('summary');
            const title = document.getElementById('summary-title');
            const text = document.getElementById('summary-text');
            const details = document.getElementById('summary-details');
            
            const percentage = Math.round((checkResults.passed / checkResults.total) * 100);
            
            let summaryClass, summaryTitle, summaryText;
            
            if (percentage >= 90) {
                summaryClass = 'success';
                summaryTitle = '🎉 ممتاز! التطبيق جاهز للاستخدام';
                summaryText = 'جميع الوظائف تعمل بشكل مثالي. يمكنك البدء في استخدام التطبيق بثقة.';
            } else if (percentage >= 70) {
                summaryClass = 'warning';
                summaryTitle = '⚠️ جيد مع بعض التحذيرات';
                summaryText = 'معظم الوظائف تعمل بشكل جيد. قد تحتاج لمراجعة بعض النقاط.';
            } else {
                summaryClass = 'error';
                summaryTitle = '❌ يحتاج إصلاحات';
                summaryText = 'هناك مشاكل تحتاج إلى إصلاح قبل الاستخدام.';
            }
            
            summary.className = `summary ${summaryClass}`;
            title.textContent = summaryTitle;
            text.textContent = summaryText;
            
            details.innerHTML = `
                <div style="margin-top: 1rem;">
                    <strong>النتائج: ${checkResults.passed}/${checkResults.total} (${percentage}%)</strong>
                </div>
                <div style="margin-top: 1rem; font-size: 0.875rem; color: #6b7280;">
                    <p>للحصول على أفضل أداء، تأكد من:</p>
                    <ul style="text-align: right; margin: 0.5rem 0;">
                        <li>استخدام متصفح حديث</li>
                        <li>تفعيل JavaScript</li>
                        <li>السماح بالإشعارات والموقع</li>
                        <li>استخدام HTTPS للميزات المتقدمة</li>
                    </ul>
                </div>
            `;
            
            summary.style.display = 'block';
        }

        function runFullCheck() {
            // Reset results
            checkResults = {
                files: 0,
                functions: 0,
                apis: 0,
                storage: 0,
                ui: 0,
                total: 0,
                passed: 0
            };
            
            // Run all checks
            checkFiles();
            
            setTimeout(() => {
                checkFunctions();
                checkAPIs();
                checkStorage();
                checkUI();
                
                setTimeout(() => {
                    showSummary();
                }, 1000);
            }, 500);
        }

        // Auto-run check on load
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(runFullCheck, 1000);
        });
    </script>
</body>
</html>
