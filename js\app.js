// Main Application Controller
class EmergencyApp {
    constructor() {
        this.currentLocation = null;
        this.isOnline = navigator.onLine;
        this.alerts = [];
        this.isAdmin = false;
        
        this.init();
    }

    async init() {
        // Show loading screen
        this.showLoading();
        
        // Initialize components
        await this.initializeLocation();
        this.initializeEventListeners();
        this.initializeServiceWorker();
        this.checkAdminStatus();
        
        // Hide loading screen after 2 seconds
        setTimeout(() => {
            this.hideLoading();
        }, 2000);
        
        // Check connection status
        this.updateConnectionStatus();
        window.addEventListener('online', () => this.updateConnectionStatus());
        window.addEventListener('offline', () => this.updateConnectionStatus());
    }

    showLoading() {
        document.getElementById('loading-screen').classList.remove('hidden');
        document.getElementById('app').classList.add('hidden');
    }

    hideLoading() {
        document.getElementById('loading-screen').classList.add('hidden');
        document.getElementById('app').classList.remove('hidden');
    }

    async initializeLocation() {
        try {
            if ('geolocation' in navigator) {
                const position = await this.getCurrentPosition();
                this.currentLocation = {
                    latitude: position.coords.latitude,
                    longitude: position.coords.longitude,
                    accuracy: position.coords.accuracy
                };
                this.updateLocationDisplay();
            } else {
                this.showManualLocationOption();
            }
        } catch (error) {
            console.error('Location error:', error);
            this.showManualLocationOption();
        }
    }

    getCurrentPosition() {
        return new Promise((resolve, reject) => {
            navigator.geolocation.getCurrentPosition(resolve, reject, {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 300000 // 5 minutes
            });
        });
    }

    updateLocationDisplay() {
        const locationDisplay = document.getElementById('location-display');
        if (this.currentLocation) {
            locationDisplay.innerHTML = `
                <p class="location-text">
                    📍 تم تحديد موقعك بدقة ${Math.round(this.currentLocation.accuracy)} متر
                </p>
                <p class="coordinates">
                    ${this.currentLocation.latitude.toFixed(6)}, ${this.currentLocation.longitude.toFixed(6)}
                </p>
                <button id="manual-location" class="manual-location-btn">تغيير الموقع</button>
            `;
        }
    }

    showManualLocationOption() {
        const locationDisplay = document.getElementById('location-display');
        locationDisplay.innerHTML = `
            <p class="location-text">⚠️ لا يمكن تحديد الموقع تلقائياً</p>
            <button id="manual-location" class="manual-location-btn">تحديد الموقع يدوياً</button>
        `;
    }

    initializeEventListeners() {
        // Emergency buttons
        document.querySelectorAll('.emergency-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const alertType = e.currentTarget.dataset.type;
                this.showConfirmationModal(alertType);
            });
        });

        // Location refresh
        document.getElementById('refresh-location').addEventListener('click', () => {
            this.initializeLocation();
        });

        // Manual location
        document.addEventListener('click', (e) => {
            if (e.target.id === 'manual-location') {
                this.showManualLocationModal();
            }
        });

        // Quick actions
        document.getElementById('view-map').addEventListener('click', () => {
            this.openMapView();
        });

        document.getElementById('recent-alerts').addEventListener('click', () => {
            this.showRecentAlerts();
        });

        document.getElementById('admin-panel').addEventListener('click', () => {
            this.openAdminPanel();
        });

        // Modal controls
        document.getElementById('close-modal').addEventListener('click', () => {
            this.hideModal('confirmation-modal');
        });

        document.getElementById('cancel-alert').addEventListener('click', () => {
            this.hideModal('confirmation-modal');
        });

        document.getElementById('confirm-alert').addEventListener('click', () => {
            this.sendAlert();
        });

        document.getElementById('close-success').addEventListener('click', () => {
            this.hideModal('success-modal');
        });

        // Close modal on backdrop click
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.hideModal(modal.id);
                }
            });
        });
    }

    showConfirmationModal(alertType) {
        const modal = document.getElementById('confirmation-modal');
        const alertTypeElement = document.getElementById('alert-type');
        const alertLocationElement = document.getElementById('alert-location');
        const alertIcon = document.querySelector('#confirmation-modal .alert-icon');
        
        // Alert type configurations
        const alertConfigs = {
            'ambulance': { icon: '🆘', title: 'إسعاف - حالة طبية طارئة', color: '#ef4444' },
            'fire': { icon: '🔥', title: 'حريق في المنطقة', color: '#f97316' },
            'civil-defense': { icon: '🛡️', title: 'دفاع مدني - طوارئ عامة', color: '#3b82f6' },
            'settler-attack': { icon: '⚠️', title: 'هجوم مستوطنين', color: '#dc2626' },
            'settlers-present': { icon: '🚷', title: 'مستوطنين في الموقع', color: '#f59e0b' },
            'intrusion': { icon: '🚨', title: 'إنذار اقتحام', color: '#7c3aed' }
        };

        const config = alertConfigs[alertType];
        alertIcon.textContent = config.icon;
        alertTypeElement.textContent = config.title;
        
        if (this.currentLocation) {
            alertLocationElement.textContent = `📍 ${this.currentLocation.latitude.toFixed(4)}, ${this.currentLocation.longitude.toFixed(4)}`;
        } else {
            alertLocationElement.textContent = '📍 الموقع غير محدد';
        }

        document.querySelector('.alert-time').textContent = `⏰ ${new Date().toLocaleString('ar-SA')}`;
        
        // Store alert type for confirmation
        modal.dataset.alertType = alertType;
        
        this.showModal('confirmation-modal');
    }

    async sendAlert() {
        const modal = document.getElementById('confirmation-modal');
        const alertType = modal.dataset.alertType;
        
        // Create alert object
        const alert = {
            id: Date.now().toString(),
            type: alertType,
            location: this.currentLocation,
            timestamp: new Date().toISOString(),
            status: 'active',
            userId: this.getUserId()
        };

        try {
            // Add to local storage
            this.alerts.unshift(alert);
            this.saveAlertsToStorage();
            
            // Send to server (if online)
            if (this.isOnline) {
                await this.sendAlertToServer(alert);
            }
            
            // Show success
            this.hideModal('confirmation-modal');
            this.showModal('success-modal');
            
            // Send local notification
            this.sendLocalNotification(alert);
            
        } catch (error) {
            console.error('Error sending alert:', error);
            alert('حدث خطأ في إرسال الإنذار. يرجى المحاولة مرة أخرى.');
        }
    }

    async sendAlertToServer(alert) {
        // Simulate API call - replace with actual endpoint
        const response = await fetch('/api/alerts', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(alert)
        });
        
        if (!response.ok) {
            throw new Error('Failed to send alert to server');
        }
        
        return response.json();
    }

    sendLocalNotification(alert) {
        if ('Notification' in window && Notification.permission === 'granted') {
            const alertConfigs = {
                'ambulance': 'تم إرسال طلب إسعاف',
                'fire': 'تم إرسال إنذار حريق',
                'civil-defense': 'تم إرسال طلب دفاع مدني',
                'settler-attack': 'تم إرسال إنذار هجوم مستوطنين',
                'settlers-present': 'تم إرسال إنذار وجود مستوطنين',
                'intrusion': 'تم إرسال إنذار اقتحام'
            };

            new Notification('درع الطوارئ', {
                body: alertConfigs[alert.type],
                icon: '/icons/icon-192x192.png',
                badge: '/icons/badge-72x72.png'
            });
        }
    }

    showModal(modalId) {
        document.getElementById(modalId).classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    }

    hideModal(modalId) {
        document.getElementById(modalId).classList.add('hidden');
        document.body.style.overflow = '';
    }

    updateConnectionStatus() {
        const statusDot = document.getElementById('connection-status');
        const statusText = document.querySelector('.status-text');
        
        this.isOnline = navigator.onLine;
        
        if (this.isOnline) {
            statusDot.className = 'status-dot online';
            statusText.textContent = 'متصل';
        } else {
            statusDot.className = 'status-dot offline';
            statusText.textContent = 'غير متصل';
        }
    }

    checkAdminStatus() {
        // Check if user is admin (implement your logic here)
        const isAdmin = localStorage.getItem('isAdmin') === 'true';
        if (isAdmin) {
            document.getElementById('admin-panel').classList.remove('hidden');
            this.isAdmin = true;
        }
    }

    getUserId() {
        let userId = localStorage.getItem('userId');
        if (!userId) {
            userId = 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            localStorage.setItem('userId', userId);
        }
        return userId;
    }

    saveAlertsToStorage() {
        localStorage.setItem('emergencyAlerts', JSON.stringify(this.alerts.slice(0, 50))); // Keep last 50 alerts
    }

    loadAlertsFromStorage() {
        const stored = localStorage.getItem('emergencyAlerts');
        if (stored) {
            this.alerts = JSON.parse(stored);
        }
    }

    async initializeServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                await navigator.serviceWorker.register('/sw.js');
                console.log('Service Worker registered successfully');
            } catch (error) {
                console.error('Service Worker registration failed:', error);
            }
        }
    }

    // Placeholder methods for future implementation
    showManualLocationModal() {
        alert('سيتم إضافة خاصية تحديد الموقع يدوياً قريباً');
    }

    openMapView() {
        alert('سيتم إضافة عرض الخريطة قريباً');
    }

    showRecentAlerts() {
        alert('سيتم إضافة عرض الإنذارات الأخيرة قريباً');
    }

    openAdminPanel() {
        window.open('admin.html', '_blank');
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.emergencyApp = new EmergencyApp();
});

// Request notification permission
if ('Notification' in window && Notification.permission === 'default') {
    Notification.requestPermission();
}
