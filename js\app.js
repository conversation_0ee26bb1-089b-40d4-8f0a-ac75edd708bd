// Main Application Controller
class EmergencyApp {
    constructor() {
        this.currentLocation = null;
        this.isOnline = navigator.onLine;
        this.alerts = [];
        this.isAdmin = false;
        
        this.init();
    }

    async init() {
        // Show loading screen
        this.showLoading();
        
        // Initialize components
        await this.initializeLocation();
        this.initializeEventListeners();
        this.initializeServiceWorker();
        this.checkAdminStatus();
        
        // Hide loading screen after 2 seconds
        setTimeout(() => {
            this.hideLoading();
        }, 2000);
        
        // Check connection status
        this.updateConnectionStatus();
        window.addEventListener('online', () => this.updateConnectionStatus());
        window.addEventListener('offline', () => this.updateConnectionStatus());
    }

    showLoading() {
        document.getElementById('loading-screen').classList.remove('hidden');
        document.getElementById('app').classList.add('hidden');
    }

    hideLoading() {
        document.getElementById('loading-screen').classList.add('hidden');
        document.getElementById('app').classList.remove('hidden');
    }

    async initializeLocation() {
        try {
            if ('geolocation' in navigator) {
                const position = await this.getCurrentPosition();
                this.currentLocation = {
                    latitude: position.coords.latitude,
                    longitude: position.coords.longitude,
                    accuracy: position.coords.accuracy
                };
                this.updateLocationDisplay();
            } else {
                this.showManualLocationOption();
            }
        } catch (error) {
            console.error('Location error:', error);
            this.showManualLocationOption();
        }
    }

    getCurrentPosition() {
        return new Promise((resolve, reject) => {
            navigator.geolocation.getCurrentPosition(resolve, reject, {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 300000 // 5 minutes
            });
        });
    }

    updateLocationDisplay() {
        const locationDisplay = document.getElementById('location-display');
        if (this.currentLocation) {
            locationDisplay.innerHTML = `
                <p class="location-text">
                    📍 تم تحديد موقعك بدقة ${Math.round(this.currentLocation.accuracy)} متر
                </p>
                <p class="coordinates">
                    ${this.currentLocation.latitude.toFixed(6)}, ${this.currentLocation.longitude.toFixed(6)}
                </p>
                <button id="manual-location" class="manual-location-btn">تغيير الموقع</button>
            `;
        }
    }

    showManualLocationOption() {
        const locationDisplay = document.getElementById('location-display');
        locationDisplay.innerHTML = `
            <p class="location-text">⚠️ لا يمكن تحديد الموقع تلقائياً</p>
            <button id="manual-location" class="manual-location-btn">تحديد الموقع يدوياً</button>
        `;
    }

    initializeEventListeners() {
        // Emergency buttons
        document.querySelectorAll('.emergency-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const alertType = e.currentTarget.dataset.type;
                this.showConfirmationModal(alertType);
            });
        });

        // Location refresh
        document.getElementById('refresh-location').addEventListener('click', () => {
            this.initializeLocation();
        });

        // Manual location
        document.addEventListener('click', (e) => {
            if (e.target.id === 'manual-location') {
                this.showManualLocationModal();
            }
        });

        // Quick actions
        document.getElementById('view-map').addEventListener('click', () => {
            this.openMapView();
        });

        document.getElementById('recent-alerts').addEventListener('click', () => {
            this.showRecentAlerts();
        });

        document.getElementById('admin-panel').addEventListener('click', () => {
            this.openAdminPanel();
        });

        // Modal controls
        document.getElementById('close-modal').addEventListener('click', () => {
            this.hideModal('confirmation-modal');
        });

        document.getElementById('cancel-alert').addEventListener('click', () => {
            this.hideModal('confirmation-modal');
        });

        document.getElementById('confirm-alert').addEventListener('click', () => {
            this.sendAlert();
        });

        document.getElementById('close-success').addEventListener('click', () => {
            this.hideModal('success-modal');
        });

        // Custom message handlers
        this.setupCustomMessageHandlers();

        // Close modal on backdrop click
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.hideModal(modal.id);
                }
            });
        });
    }

    setupCustomMessageHandlers() {
        // Message counter
        const customMessage = document.getElementById('custom-message');
        const messageCount = document.getElementById('message-count');

        if (customMessage && messageCount) {
            customMessage.addEventListener('input', (e) => {
                const count = e.target.value.length;
                messageCount.textContent = count;

                // Change color based on length
                if (count > 450) {
                    messageCount.style.color = '#dc2626';
                } else if (count > 350) {
                    messageCount.style.color = '#f59e0b';
                } else {
                    messageCount.style.color = '#6b7280';
                }
            });
        }

        // Template buttons
        document.querySelectorAll('.template-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const template = e.target.dataset.template;
                if (customMessage) {
                    const currentText = customMessage.value;
                    const newText = currentText ? `${currentText}\n${template}` : template;

                    if (newText.length <= 500) {
                        customMessage.value = newText;
                        customMessage.dispatchEvent(new Event('input'));
                        customMessage.focus();
                    }
                }
            });
        });
    }

    async showConfirmationModal(alertType) {
        const modal = document.getElementById('confirmation-modal');
        const alertTypeElement = document.getElementById('alert-type');
        const alertLocationElement = document.getElementById('alert-location');
        const alertIcon = document.querySelector('#confirmation-modal .alert-icon');
        const alertPreview = document.getElementById('alert-preview');

        // Alert type configurations
        const alertConfigs = {
            'ambulance': { icon: '🆘', title: 'إسعاف - حالة طبية طارئة', color: '#ef4444', priority: 'critical' },
            'fire': { icon: '🔥', title: 'حريق في المنطقة', color: '#f97316', priority: 'critical' },
            'civil-defense': { icon: '🛡️', title: 'دفاع مدني - طوارئ عامة', color: '#3b82f6', priority: 'high' },
            'settler-attack': { icon: '⚠️', title: 'هجوم مستوطنين', color: '#dc2626', priority: 'critical' },
            'settlers-present': { icon: '🚷', title: 'مستوطنين في الموقع', color: '#f59e0b', priority: 'high' },
            'intrusion': { icon: '🚨', title: 'إنذار اقتحام', color: '#7c3aed', priority: 'critical' }
        };

        const config = alertConfigs[alertType];
        alertIcon.textContent = config.icon;
        alertTypeElement.textContent = config.title;

        // Set alert preview priority class
        alertPreview.className = `alert-preview ${config.priority}`;

        if (this.currentLocation) {
            alertLocationElement.textContent = `📍 ${this.currentLocation.latitude.toFixed(4)}, ${this.currentLocation.longitude.toFixed(4)}`;
        } else {
            alertLocationElement.textContent = '📍 الموقع غير محدد';
        }

        document.querySelector('.alert-time').textContent = `⏰ ${new Date().toLocaleString('ar-SA')}`;

        // Clear custom message
        const customMessage = document.getElementById('custom-message');
        if (customMessage) {
            customMessage.value = '';
            customMessage.dispatchEvent(new Event('input'));
        }

        // Display sender information
        await this.displaySenderInfo();

        // Store alert type for confirmation
        modal.dataset.alertType = alertType;

        this.showModal('confirmation-modal');
    }

    async displaySenderInfo() {
        const senderDetails = document.getElementById('sender-details');
        const currentUser = this.getCurrentUser();
        const userIP = await this.getUserIP();
        const deviceInfo = this.getDeviceInfo();

        if (senderDetails) {
            senderDetails.innerHTML = `
                <div class="sender-detail-item">
                    <span class="sender-detail-label">الاسم:</span>
                    <span class="sender-detail-value">${currentUser?.fullName || 'غير محدد'}</span>
                </div>
                <div class="sender-detail-item">
                    <span class="sender-detail-label">رقم الهاتف:</span>
                    <span class="sender-detail-value">${currentUser?.phoneNumber || 'غير محدد'}</span>
                </div>
                <div class="sender-detail-item">
                    <span class="sender-detail-label">نوع الحساب:</span>
                    <span class="sender-detail-value">${this.getRoleText(currentUser?.userRole) || 'غير محدد'}</span>
                </div>
                <div class="sender-detail-item">
                    <span class="sender-detail-label">المنطقة:</span>
                    <span class="sender-detail-value">${currentUser?.locationArea || 'غير محدد'}</span>
                </div>
                <div class="sender-detail-item">
                    <span class="sender-detail-label">عنوان IP:</span>
                    <span class="sender-detail-value">${userIP}</span>
                </div>
                <div class="sender-detail-item">
                    <span class="sender-detail-label">الجهاز:</span>
                    <span class="sender-detail-value">${deviceInfo}</span>
                </div>
            `;
        }
    }

    async sendAlert() {
        const modal = document.getElementById('confirmation-modal');
        const alertType = modal.dataset.alertType;
        const customMessage = document.getElementById('custom-message')?.value || '';
        const currentUser = this.getCurrentUser();

        // Create enhanced alert object
        const alert = {
            id: Date.now().toString(),
            type: alertType,
            location: this.currentLocation,
            timestamp: new Date().toISOString(),
            status: 'active',
            userId: this.getUserId(),
            customMessage: customMessage.trim(),
            senderInfo: {
                fullName: currentUser?.fullName || 'غير محدد',
                phoneNumber: currentUser?.phoneNumber || 'غير محدد',
                userRole: currentUser?.userRole || 'citizen',
                locationArea: currentUser?.locationArea || 'غير محدد',
                ipAddress: await this.getUserIP(),
                deviceInfo: this.getDeviceInfo(),
                userAgent: navigator.userAgent
            },
            priority: this.getAlertPriority(alertType)
        };

        try {
            // Add to local storage
            this.alerts.unshift(alert);
            this.saveAlertsToStorage();

            // Send to server (if online)
            if (this.isOnline) {
                await this.sendAlertToServer(alert);
            }

            // Send SMS notifications to all subscribers
            await this.sendSMSNotifications(alert);

            // Show success
            this.hideModal('confirmation-modal');
            this.showModal('success-modal');

            // Send local notification
            this.sendLocalNotification(alert);

            // Broadcast to other users via WebSocket
            if (window.webSocketManager) {
                window.webSocketManager.sendAlert(alert);
            }

        } catch (error) {
            console.error('Error sending alert:', error);
            alert('حدث خطأ في إرسال الإنذار. يرجى المحاولة مرة أخرى.');
        }
    }

    getAlertPriority(alertType) {
        const priorities = {
            'ambulance': 'critical',
            'fire': 'critical',
            'settler-attack': 'critical',
            'intrusion': 'critical',
            'civil-defense': 'high',
            'settlers-present': 'medium'
        };
        return priorities[alertType] || 'medium';
    }

    async sendSMSNotifications(alert) {
        if (!window.smsService) return;

        try {
            // Get all registered users (in a real app, this would be from server)
            const allUsers = this.getAllRegisteredUsers();
            const nearbyUsers = this.getNearbyUsers(alert.location, 5000); // 5km radius

            // Send to nearby users and emergency contacts
            const recipients = [...nearbyUsers];

            // Add emergency contacts based on alert type
            const emergencyContacts = this.getEmergencyContacts(alert.type);
            recipients.push(...emergencyContacts);

            // Send SMS to each recipient
            for (const user of recipients) {
                if (user.phoneNumber && user.phoneNumber !== alert.senderInfo.phoneNumber) {
                    try {
                        await window.smsService.sendEmergencyAlert(user.phoneNumber, {
                            ...alert,
                            senderName: alert.senderInfo.fullName,
                            senderPhone: alert.senderInfo.phoneNumber
                        });
                    } catch (smsError) {
                        console.error('Failed to send SMS to:', user.phoneNumber, smsError);
                    }
                }
            }
        } catch (error) {
            console.error('Error sending SMS notifications:', error);
        }
    }

    getAllRegisteredUsers() {
        // In a real app, this would fetch from server
        const users = JSON.parse(localStorage.getItem('emergencyShieldUsers') || '{}');
        return Object.values(users);
    }

    getNearbyUsers(location, radius) {
        if (!location) return [];

        const allUsers = this.getAllRegisteredUsers();
        return allUsers.filter(user => {
            if (!user.lastKnownLocation) return false;

            const distance = this.calculateDistance(
                location.latitude, location.longitude,
                user.lastKnownLocation.latitude, user.lastKnownLocation.longitude
            );

            return distance <= radius;
        });
    }

    getEmergencyContacts(alertType) {
        // Emergency service numbers (in a real app, these would be from database)
        const emergencyContacts = {
            'ambulance': [
                { phoneNumber: '+970101', name: 'الإسعاف الفلسطيني', role: 'emergency' },
                { phoneNumber: '+970102', name: 'المستشفى الرئيسي', role: 'medical' }
            ],
            'fire': [
                { phoneNumber: '+970103', name: 'الدفاع المدني - الإطفاء', role: 'fire' },
                { phoneNumber: '+970104', name: 'إطفاء المنطقة', role: 'fire' }
            ],
            'civil-defense': [
                { phoneNumber: '+970105', name: 'الدفاع المدني', role: 'emergency' }
            ],
            'settler-attack': [
                { phoneNumber: '+970106', name: 'الأمن الوطني', role: 'security' },
                { phoneNumber: '+970107', name: 'لجنة الحماية الشعبية', role: 'security' }
            ],
            'settlers-present': [
                { phoneNumber: '+970106', name: 'الأمن الوطني', role: 'security' }
            ],
            'intrusion': [
                { phoneNumber: '+970106', name: 'الأمن الوطني', role: 'security' },
                { phoneNumber: '+970105', name: 'الدفاع المدني', role: 'emergency' }
            ]
        };

        return emergencyContacts[alertType] || [];
    }

    async sendAlertToServer(alert) {
        // Simulate API call - replace with actual endpoint
        const response = await fetch('/api/alerts', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(alert)
        });
        
        if (!response.ok) {
            throw new Error('Failed to send alert to server');
        }
        
        return response.json();
    }

    sendLocalNotification(alert) {
        // Use enhanced notification system if available
        if (window.enhancedNotificationManager) {
            window.enhancedNotificationManager.showEmergencyNotification(alert);
            return;
        }

        // Fallback to browser notifications
        if ('Notification' in window && Notification.permission === 'granted') {
            const alertConfigs = {
                'ambulance': 'تم إرسال طلب إسعاف',
                'fire': 'تم إرسال إنذار حريق',
                'civil-defense': 'تم إرسال طلب دفاع مدني',
                'settler-attack': 'تم إرسال إنذار هجوم مستوطنين',
                'settlers-present': 'تم إرسال إنذار وجود مستوطنين',
                'intrusion': 'تم إرسال إنذار اقتحام'
            };

            new Notification('درع الطوارئ', {
                body: alertConfigs[alert.type],
                icon: '/icons/icon-192x192.png',
                badge: '/icons/badge-72x72.png'
            });
        }
    }

    showModal(modalId) {
        document.getElementById(modalId).classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    }

    hideModal(modalId) {
        document.getElementById(modalId).classList.add('hidden');
        document.body.style.overflow = '';
    }

    updateConnectionStatus() {
        const statusDot = document.getElementById('connection-status');
        const statusText = document.querySelector('.status-text');
        
        this.isOnline = navigator.onLine;
        
        if (this.isOnline) {
            statusDot.className = 'status-dot online';
            statusText.textContent = 'متصل';
        } else {
            statusDot.className = 'status-dot offline';
            statusText.textContent = 'غير متصل';
        }
    }

    checkAdminStatus() {
        // Check if user is admin (implement your logic here)
        const isAdmin = localStorage.getItem('isAdmin') === 'true';
        if (isAdmin) {
            document.getElementById('admin-panel').classList.remove('hidden');
            this.isAdmin = true;
        }
    }

    getUserId() {
        let userId = localStorage.getItem('userId');
        if (!userId) {
            userId = 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            localStorage.setItem('userId', userId);
        }
        return userId;
    }

    getCurrentUser() {
        const saved = localStorage.getItem('currentUser');
        return saved ? JSON.parse(saved) : null;
    }

    async getUserIP() {
        try {
            const response = await fetch('https://api.ipify.org?format=json');
            const data = await response.json();
            return data.ip;
        } catch (error) {
            console.error('Failed to get IP:', error);
            return 'unknown';
        }
    }

    getDeviceInfo() {
        const ua = navigator.userAgent;
        let device = 'Unknown';

        if (/Android/i.test(ua)) {
            device = 'Android';
        } else if (/iPhone|iPad|iPod/i.test(ua)) {
            device = 'iOS';
        } else if (/Windows/i.test(ua)) {
            device = 'Windows';
        } else if (/Mac/i.test(ua)) {
            device = 'Mac';
        } else if (/Linux/i.test(ua)) {
            device = 'Linux';
        }

        return device;
    }

    getRoleText(role) {
        const roles = {
            'citizen': 'مواطن',
            'emergency': 'طوارئ',
            'security': 'أمن',
            'medical': 'طبي',
            'fire': 'إطفاء',
            'admin': 'مسؤول'
        };
        return roles[role] || role;
    }

    calculateDistance(lat1, lon1, lat2, lon2) {
        const R = 6371e3; // Earth's radius in meters
        const φ1 = lat1 * Math.PI / 180;
        const φ2 = lat2 * Math.PI / 180;
        const Δφ = (lat2 - lat1) * Math.PI / 180;
        const Δλ = (lon2 - lon1) * Math.PI / 180;

        const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
                  Math.cos(φ1) * Math.cos(φ2) *
                  Math.sin(Δλ/2) * Math.sin(Δλ/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

        return R * c;
    }

    saveAlertsToStorage() {
        localStorage.setItem('emergencyAlerts', JSON.stringify(this.alerts.slice(0, 50))); // Keep last 50 alerts
    }

    loadAlertsFromStorage() {
        const stored = localStorage.getItem('emergencyAlerts');
        if (stored) {
            this.alerts = JSON.parse(stored);
        }
    }

    async initializeServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                await navigator.serviceWorker.register('/sw.js');
                console.log('Service Worker registered successfully');
            } catch (error) {
                console.error('Service Worker registration failed:', error);
            }
        }
    }

    // Placeholder methods for future implementation
    showManualLocationModal() {
        alert('سيتم إضافة خاصية تحديد الموقع يدوياً قريباً');
    }

    openMapView() {
        alert('سيتم إضافة عرض الخريطة قريباً');
    }

    showRecentAlerts() {
        alert('سيتم إضافة عرض الإنذارات الأخيرة قريباً');
    }

    openAdminPanel() {
        window.open('admin.html', '_blank');
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.emergencyApp = new EmergencyApp();
});

// Request notification permission
if ('Notification' in window && Notification.permission === 'default') {
    Notification.requestPermission();
}
