<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار رمز التحقق - درع الطوارئ</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
            margin: 0;
            padding: 2rem;
            color: white;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2rem;
            color: #333;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: #f8fafc;
            border-radius: 12px;
            border-left: 4px solid #3b82f6;
        }
        
        .test-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 12px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            margin: 0.5rem;
            transition: all 0.2s;
            display: block;
            width: 100%;
        }
        
        .test-btn:hover {
            background: #2563eb;
            transform: translateY(-2px);
        }
        
        .phone-input {
            display: flex;
            gap: 0.5rem;
            margin: 1rem 0;
        }
        
        .phone-input input {
            flex: 1;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 1rem;
        }
        
        .result-box {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 8px;
            font-family: monospace;
            font-size: 0.875rem;
        }
        
        .success {
            background: #f0fdf4;
            border: 1px solid #16a34a;
            color: #16a34a;
        }
        
        .error {
            background: #fef2f2;
            border: 1px solid #dc2626;
            color: #dc2626;
        }
        
        .info {
            background: #eff6ff;
            border: 1px solid #3b82f6;
            color: #3b82f6;
        }
        
        .code-display {
            background: #1f2937;
            color: #10b981;
            padding: 1rem;
            border-radius: 8px;
            font-family: monospace;
            font-size: 1.2rem;
            text-align: center;
            margin: 1rem 0;
            border: 2px solid #10b981;
        }
        
        .instructions {
            background: #fffbeb;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .instructions h4 {
            color: #92400e;
            margin-top: 0;
        }
        
        .instructions p {
            color: #92400e;
            margin: 0.5rem 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>📱 اختبار رمز التحقق</h1>
            <p>اختبار خدمة إرسال رموز التحقق عبر SMS</p>
        </div>

        <div class="instructions">
            <h4>📋 كيفية الاختبار:</h4>
            <p>1. أدخل رقم هاتف بالصيغة الصحيحة (+970xxxxxxxxx)</p>
            <p>2. اضغط على "إرسال رمز التحقق"</p>
            <p>3. سيظهر الرمز في نافذة منبثقة وفي وحدة التحكم</p>
            <p>4. استخدم الرمز في صفحة المصادقة</p>
        </div>

        <div class="test-section">
            <h3>إرسال رمز التحقق</h3>
            <div class="phone-input">
                <input type="tel" id="phone-number" placeholder="أدخل رقم الهاتف (+970xxxxxxxxx)" value="+970591234567">
                <button class="test-btn" onclick="sendVerificationCode()" style="width: auto; margin: 0;">
                    إرسال
                </button>
            </div>
            <div id="sms-result"></div>
        </div>

        <div class="test-section">
            <h3>اختبار رموز مختلفة</h3>
            <button class="test-btn" onclick="testRandomCode()">
                إنشاء رمز عشوائي
            </button>
            <button class="test-btn" onclick="testFixedCode()">
                استخدام رمز ثابت (123456)
            </button>
            <div id="code-result"></div>
        </div>

        <div class="test-section">
            <h3>اختبار التحقق من الرمز</h3>
            <div class="phone-input">
                <input type="text" id="verification-code" placeholder="أدخل رمز التحقق (6 أرقام)" maxlength="6">
                <button class="test-btn" onclick="verifyCode()" style="width: auto; margin: 0;">
                    تحقق
                </button>
            </div>
            <div id="verify-result"></div>
        </div>

        <div class="test-section">
            <h3>معلومات النظام</h3>
            <div id="system-info"></div>
        </div>
    </div>

    <!-- Load SMS service -->
    <script src="config.js"></script>
    <script src="fix-errors.js"></script>
    <script src="js/sms-service.js"></script>

    <script>
        let lastSentCode = null;
        let lastPhoneNumber = null;

        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result-box ${type}">${message}</div>`;
        }

        async function sendVerificationCode() {
            const phoneNumber = document.getElementById('phone-number').value.trim();
            
            if (!phoneNumber) {
                showResult('sms-result', 'يرجى إدخال رقم الهاتف', 'error');
                return;
            }

            try {
                // Generate verification code
                const code = Math.floor(100000 + Math.random() * 900000).toString();
                lastSentCode = code;
                lastPhoneNumber = phoneNumber;

                showResult('sms-result', 'جاري إرسال رمز التحقق...', 'info');

                // Use SMS service if available
                if (window.smsService) {
                    const result = await window.smsService.sendVerificationCode(phoneNumber, code);
                    
                    if (result.success) {
                        showResult('sms-result', `
                            ✅ تم إرسال رمز التحقق بنجاح<br>
                            📱 إلى: ${phoneNumber}<br>
                            🔑 الرمز: <span class="code-display">${code}</span>
                            📝 معرف الرسالة: ${result.messageId}
                        `, 'success');
                    } else {
                        showResult('sms-result', `❌ فشل في إرسال الرمز: ${result.error}`, 'error');
                    }
                } else {
                    // Fallback simulation
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    
                    showResult('sms-result', `
                        ✅ تم إرسال رمز التحقق (محاكاة)<br>
                        📱 إلى: ${phoneNumber}<br>
                        🔑 الرمز: <span class="code-display">${code}</span>
                    `, 'success');

                    // Show alert with code
                    setTimeout(() => {
                        alert(`📱 رمز التحقق: ${code}\n\nتم إرسال الرمز إلى ${phoneNumber}`);
                    }, 500);
                }

                console.log(`📱 SMS sent to ${phoneNumber}: ${code}`);
                
            } catch (error) {
                console.error('Error sending SMS:', error);
                showResult('sms-result', `❌ خطأ في إرسال الرمز: ${error.message}`, 'error');
            }
        }

        function testRandomCode() {
            const code = Math.floor(100000 + Math.random() * 900000).toString();
            lastSentCode = code;
            
            showResult('code-result', `
                🎲 رمز عشوائي تم إنشاؤه:<br>
                <span class="code-display">${code}</span>
                يمكنك استخدام هذا الرمز في صفحة المصادقة
            `, 'success');
        }

        function testFixedCode() {
            const code = '123456';
            lastSentCode = code;
            
            showResult('code-result', `
                🔢 رمز ثابت للاختبار:<br>
                <span class="code-display">${code}</span>
                يمكنك استخدام هذا الرمز دائماً للاختبار
            `, 'success');
        }

        function verifyCode() {
            const enteredCode = document.getElementById('verification-code').value.trim();
            
            if (!enteredCode) {
                showResult('verify-result', 'يرجى إدخال رمز التحقق', 'error');
                return;
            }

            if (enteredCode.length !== 6) {
                showResult('verify-result', 'رمز التحقق يجب أن يكون 6 أرقام', 'error');
                return;
            }

            // Check against last sent code or accept any 6-digit code for demo
            if (lastSentCode && enteredCode === lastSentCode) {
                showResult('verify-result', `
                    ✅ رمز التحقق صحيح!<br>
                    🔑 الرمز المدخل: ${enteredCode}<br>
                    📱 الهاتف: ${lastPhoneNumber || 'غير محدد'}
                `, 'success');
            } else if (/^\d{6}$/.test(enteredCode)) {
                showResult('verify-result', `
                    ✅ رمز التحقق مقبول (وضع التجربة)<br>
                    🔑 الرمز المدخل: ${enteredCode}<br>
                    💡 في وضع التجربة، أي رمز مكون من 6 أرقام مقبول
                `, 'success');
            } else {
                showResult('verify-result', `
                    ❌ رمز التحقق غير صحيح<br>
                    🔑 الرمز المدخل: ${enteredCode}<br>
                    💡 تأكد من إدخال 6 أرقام فقط
                `, 'error');
            }
        }

        function updateSystemInfo() {
            const info = {
                'SMS Service': window.smsService ? '✅ محمل' : '❌ غير محمل',
                'Notification API': 'Notification' in window ? '✅ مدعوم' : '❌ غير مدعوم',
                'Notification Permission': 'Notification' in window ? Notification.permission : 'غير متاح',
                'Local Storage': typeof localStorage !== 'undefined' ? '✅ متاح' : '❌ غير متاح',
                'Console Logging': '✅ مفعل'
            };

            let infoHtml = '';
            for (const [key, value] of Object.entries(info)) {
                infoHtml += `<div style="display: flex; justify-content: space-between; padding: 0.5rem 0; border-bottom: 1px solid #e5e7eb;">
                    <span><strong>${key}:</strong></span>
                    <span>${value}</span>
                </div>`;
            }

            document.getElementById('system-info').innerHTML = infoHtml;
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            updateSystemInfo();
            
            // Request notification permission
            if ('Notification' in window && Notification.permission === 'default') {
                Notification.requestPermission().then(permission => {
                    updateSystemInfo();
                });
            }
        });

        // Auto-focus phone input
        document.getElementById('phone-number').focus();
    </script>
</body>
</html>
