<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - درع الطوارئ</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="auth-styles.css">
    <meta name="theme-color" content="#dc2626">
</head>
<body class="auth-body">
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="shield-icon">🛡️</div>
            <h2>درع الطوارئ</h2>
            <div class="loading-spinner"></div>
            <p>جاري التحميل...</p>
        </div>
    </div>

    <!-- Authentication Container -->
    <div id="auth-container" class="auth-container hidden">
        <!-- Welcome Screen -->
        <div id="welcome-screen" class="auth-screen active">
            <div class="auth-header">
                <div class="shield-icon">🛡️</div>
                <h1>مرحباً بك في درع الطوارئ</h1>
                <p>نظام الإنذار الذكي للطوارئ</p>
                <p class="subtitle">أمنك وأمان عائلتك بضغطة زر</p>
            </div>
            
            <div class="auth-features">
                <div class="feature-item">
                    <span class="feature-icon">🚨</span>
                    <span>إنذارات فورية</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">📍</span>
                    <span>تحديد الموقع الدقيق</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">👥</span>
                    <span>إشعار جميع المشتركين</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">🔒</span>
                    <span>آمن ومشفر</span>
                </div>
            </div>

            <div class="auth-actions">
                <button id="start-registration" class="auth-btn primary">
                    <span>📱</span>
                    تسجيل حساب جديد
                </button>
                <button id="start-login" class="auth-btn secondary">
                    <span>🔑</span>
                    تسجيل الدخول
                </button>
            </div>
        </div>

        <!-- Phone Registration Screen -->
        <div id="phone-screen" class="auth-screen">
            <div class="auth-header">
                <button class="back-btn" onclick="showScreen('welcome-screen')">←</button>
                <h2>تسجيل رقم الهاتف</h2>
                <p>سنرسل لك رمز التحقق عبر رسالة نصية</p>
            </div>

            <form id="phone-form" class="auth-form">
                <div class="form-group">
                    <label for="full-name">الاسم الكامل</label>
                    <input type="text" id="full-name" name="fullName" required 
                           placeholder="أدخل اسمك الكامل">
                </div>

                <div class="form-group">
                    <label for="phone-number">رقم الهاتف</label>
                    <div class="phone-input">
                        <select id="country-code" name="countryCode">
                            <option value="+970">🇵🇸 +970</option>
                            <option value="+972">🇮🇱 +972</option>
                            <option value="+962">🇯🇴 +962</option>
                            <option value="+961">🇱🇧 +961</option>
                            <option value="+963">🇸🇾 +963</option>
                            <option value="+20">🇪🇬 +20</option>
                            <option value="+966">🇸🇦 +966</option>
                        </select>
                        <input type="tel" id="phone-number" name="phoneNumber" required 
                               placeholder="5xxxxxxxx" pattern="[0-9]{9}">
                    </div>
                </div>

                <div class="form-group">
                    <label for="user-role">نوع الحساب</label>
                    <select id="user-role" name="userRole" required>
                        <option value="">اختر نوع الحساب</option>
                        <option value="citizen">مواطن</option>
                        <option value="emergency">طوارئ</option>
                        <option value="security">أمن</option>
                        <option value="medical">طبي</option>
                        <option value="fire">إطفاء</option>
                        <option value="admin">مسؤول</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="location-area">المنطقة</label>
                    <input type="text" id="location-area" name="locationArea" required 
                           placeholder="مثال: القدس، رام الله، غزة">
                </div>

                <div class="form-group checkbox-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="terms-agreement" required>
                        <span class="checkmark"></span>
                        أوافق على <a href="#" onclick="showTerms()">شروط الاستخدام</a> و <a href="#" onclick="showPrivacy()">سياسة الخصوصية</a>
                    </label>
                </div>

                <button type="submit" class="auth-btn primary full-width">
                    <span>📤</span>
                    إرسال رمز التحقق
                </button>
            </form>
        </div>

        <!-- SMS Verification Screen -->
        <div id="verification-screen" class="auth-screen">
            <div class="auth-header">
                <button class="back-btn" onclick="showScreen('phone-screen')">←</button>
                <h2>تأكيد رقم الهاتف</h2>
                <p>أدخل الرمز المرسل إلى <span id="sent-phone-number"></span></p>
                <div class="verification-info">
                    <div class="info-box">
                        <p><strong>📱 للتجربة:</strong> سيظهر رمز التحقق في نافذة منبثقة</p>
                        <p><strong>🔢 أو استخدم:</strong> أي رقم مكون من 6 أرقام (مثل: 123456)</p>
                    </div>
                </div>
            </div>

            <form id="verification-form" class="auth-form">
                <div class="verification-input">
                    <input type="text" id="code-1" maxlength="1" pattern="[0-9]" required>
                    <input type="text" id="code-2" maxlength="1" pattern="[0-9]" required>
                    <input type="text" id="code-3" maxlength="1" pattern="[0-9]" required>
                    <input type="text" id="code-4" maxlength="1" pattern="[0-9]" required>
                    <input type="text" id="code-5" maxlength="1" pattern="[0-9]" required>
                    <input type="text" id="code-6" maxlength="1" pattern="[0-9]" required>
                </div>

                <div class="verification-info">
                    <p>لم تستلم الرمز؟</p>
                    <button type="button" id="resend-code" class="link-btn" disabled>
                        إعادة الإرسال (<span id="countdown">60</span>)
                    </button>
                </div>

                <button type="submit" class="auth-btn primary full-width">
                    <span>✅</span>
                    تأكيد الرمز
                </button>
            </form>
        </div>

        <!-- Login Screen -->
        <div id="login-screen" class="auth-screen">
            <div class="auth-header">
                <button class="back-btn" onclick="showScreen('welcome-screen')">←</button>
                <h2>تسجيل الدخول</h2>
                <p>أدخل رقم هاتفك المسجل</p>
            </div>

            <form id="login-form" class="auth-form">
                <div class="form-group">
                    <label for="login-phone">رقم الهاتف</label>
                    <div class="phone-input">
                        <select id="login-country-code" name="countryCode">
                            <option value="+970">🇵🇸 +970</option>
                            <option value="+972">🇮🇱 +972</option>
                            <option value="+962">🇯🇴 +962</option>
                            <option value="+961">🇱🇧 +961</option>
                            <option value="+963">🇸🇾 +963</option>
                            <option value="+20">🇪🇬 +20</option>
                            <option value="+966">🇸🇦 +966</option>
                        </select>
                        <input type="tel" id="login-phone" name="phoneNumber" required 
                               placeholder="5xxxxxxxx" pattern="[0-9]{9}">
                    </div>
                </div>

                <button type="submit" class="auth-btn primary full-width">
                    <span>🔑</span>
                    إرسال رمز الدخول
                </button>

                <div class="auth-footer">
                    <p>ليس لديك حساب؟ <button type="button" class="link-btn" onclick="showScreen('phone-screen')">سجل الآن</button></p>
                </div>
            </form>
        </div>

        <!-- Success Screen -->
        <div id="success-screen" class="auth-screen">
            <div class="auth-header">
                <div class="success-icon">✅</div>
                <h2>تم التسجيل بنجاح!</h2>
                <p>مرحباً بك في درع الطوارئ</p>
            </div>

            <div class="user-info" id="user-info-display">
                <!-- User info will be populated here -->
            </div>

            <div class="auth-actions">
                <button id="continue-to-app" class="auth-btn primary full-width">
                    <span>🚀</span>
                    الدخول إلى التطبيق
                </button>
            </div>
        </div>
    </div>

    <!-- Terms Modal -->
    <div id="terms-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>شروط الاستخدام</h3>
                <button class="close-btn" onclick="hideModal('terms-modal')">✕</button>
            </div>
            <div class="modal-body">
                <div class="terms-content">
                    <h4>1. قبول الشروط</h4>
                    <p>باستخدام تطبيق "درع الطوارئ"، فإنك توافق على هذه الشروط والأحكام.</p>
                    
                    <h4>2. الاستخدام المسؤول</h4>
                    <p>يجب استخدام التطبيق فقط في حالات الطوارئ الحقيقية. الاستخدام الخاطئ قد يؤدي إلى إيقاف الحساب.</p>
                    
                    <h4>3. دقة المعلومات</h4>
                    <p>يجب تقديم معلومات صحيحة ودقيقة عند التسجيل وإرسال الإنذارات.</p>
                    
                    <h4>4. الخصوصية</h4>
                    <p>نحن نحترم خصوصيتك ونحمي بياناتك الشخصية وفقاً لسياسة الخصوصية.</p>
                    
                    <h4>5. المسؤولية</h4>
                    <p>التطبيق مساعد في حالات الطوارئ ولا يغني عن الاتصال بخدمات الطوارئ الرسمية.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Privacy Modal -->
    <div id="privacy-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>سياسة الخصوصية</h3>
                <button class="close-btn" onclick="hideModal('privacy-modal')">✕</button>
            </div>
            <div class="modal-body">
                <div class="privacy-content">
                    <h4>جمع البيانات</h4>
                    <p>نجمع رقم الهاتف، الاسم، الموقع الجغرافي فقط لأغراض الطوارئ.</p>
                    
                    <h4>استخدام البيانات</h4>
                    <p>البيانات تستخدم فقط لإرسال وتلقي إنذارات الطوارئ وتحديد المواقع.</p>
                    
                    <h4>مشاركة البيانات</h4>
                    <p>لا نشارك بياناتك مع أطراف ثالثة إلا في حالات الطوارئ مع الجهات المختصة.</p>
                    
                    <h4>أمان البيانات</h4>
                    <p>جميع البيانات مشفرة ومحمية بأعلى معايير الأمان.</p>
                    
                    <h4>حقوقك</h4>
                    <p>يمكنك طلب حذف حسابك وبياناتك في أي وقت.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="config.js"></script>
    <script src="fix-errors.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/sms-service.js"></script>
</body>
</html>
