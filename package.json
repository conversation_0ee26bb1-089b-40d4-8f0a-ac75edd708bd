{"name": "emergency-shield", "version": "2.0.0", "description": "درع الطوارئ - نظام إنذار ذكي وفعال للطوارئ", "main": "electron-main.js", "homepage": "./", "author": {"name": "Emergency Shield Team", "email": "<EMAIL>"}, "license": "MIT", "keywords": ["emergency", "alert", "safety", "arabic", "palestine"], "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "build-win32": "electron-builder --win --ia32", "build-win64": "electron-builder --win --x64", "build-all": "electron-builder --win --mac --linux", "pack": "electron-builder --dir", "dist": "npm run build", "postinstall": "electron-builder install-app-deps", "clean": "<PERSON><PERSON><PERSON> dist build", "rebuild": "electron-rebuild"}, "build": {"appId": "com.emergencyshield.app", "productName": "درع الطوارئ - Emergency Shield", "directories": {"output": "dist", "buildResources": "build-resources"}, "files": ["**/*", "!node_modules/**/*", "!dist/**/*", "!build-resources/**/*", "!*.md", "!*.py", "!*.sh", "!*.bat", "!test-*", "!quick-test.html", "!final-check.html"], "extraFiles": [{"from": "icons", "to": "icons"}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64", "ia32"]}], "icon": "icons/icon.ico", "requestedExecutionLevel": "asInvoker", "artifactName": "${productName}-${version}-${arch}.${ext}", "publisherName": "Emergency Shield Team"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "allowElevation": true, "installerIcon": "icons/icon.ico", "uninstallerIcon": "icons/icon.ico", "installerHeaderIcon": "icons/icon.ico", "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "درع الطوارئ", "include": "installer.nsh", "language": "1025", "multiLanguageInstaller": true, "deleteAppDataOnUninstall": false, "runAfterFinish": true, "menuCategory": "Emergency & Safety"}, "portable": {"artifactName": "${productName}-${version}-portable-${arch}.${ext}"}, "mac": {"target": "dmg", "icon": "icons/icon.icns", "category": "public.app-category.utilities"}, "linux": {"target": ["AppImage", "deb", "rpm"], "icon": "icons/icon.png", "category": "Utility"}, "compression": "maximum", "removePackageScripts": true}, "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4", "electron-rebuild": "^3.2.9", "rimraf": "^5.0.5"}, "dependencies": {"electron-updater": "^6.1.4", "electron-log": "^4.4.8", "electron-store": "^8.1.0", "node-notifier": "^10.0.1"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/emergency-shield/app.git"}, "bugs": {"url": "https://github.com/emergency-shield/app/issues"}}