// Database and Security Manager
class DatabaseManager {
    constructor() {
        this.dbName = 'EmergencyShieldDB';
        this.dbVersion = 1;
        this.db = null;
        this.isInitialized = false;
        this.encryptionKey = null;
        
        this.init();
    }

    async init() {
        try {
            await this.initializeDatabase();
            this.setupEncryption();
            this.isInitialized = true;
            console.log('Database initialized successfully');
        } catch (error) {
            console.error('Database initialization failed:', error);
        }
    }

    // Initialize IndexedDB
    async initializeDatabase() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.dbVersion);

            request.onerror = () => {
                reject(new Error('Failed to open database'));
            };

            request.onsuccess = () => {
                this.db = request.result;
                resolve(this.db);
            };

            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                
                // Create object stores
                this.createObjectStores(db);
            };
        });
    }

    createObjectStores(db) {
        // Alerts store
        if (!db.objectStoreNames.contains('alerts')) {
            const alertsStore = db.createObjectStore('alerts', { keyPath: 'id' });
            alertsStore.createIndex('timestamp', 'timestamp');
            alertsStore.createIndex('type', 'type');
            alertsStore.createIndex('status', 'status');
            alertsStore.createIndex('priority', 'priority');
            alertsStore.createIndex('userId', 'userId');
        }

        // Users store
        if (!db.objectStoreNames.contains('users')) {
            const usersStore = db.createObjectStore('users', { keyPath: 'id' });
            usersStore.createIndex('email', 'email', { unique: true });
            usersStore.createIndex('role', 'role');
            usersStore.createIndex('status', 'status');
        }

        // Locations store
        if (!db.objectStoreNames.contains('locations')) {
            const locationsStore = db.createObjectStore('locations', { keyPath: 'id' });
            locationsStore.createIndex('timestamp', 'timestamp');
            locationsStore.createIndex('userId', 'userId');
        }

        // Settings store
        if (!db.objectStoreNames.contains('settings')) {
            const settingsStore = db.createObjectStore('settings', { keyPath: 'key' });
        }

        // Logs store
        if (!db.objectStoreNames.contains('logs')) {
            const logsStore = db.createObjectStore('logs', { keyPath: 'id' });
            logsStore.createIndex('timestamp', 'timestamp');
            logsStore.createIndex('level', 'level');
            logsStore.createIndex('category', 'category');
        }

        // Offline sync queue
        if (!db.objectStoreNames.contains('syncQueue')) {
            const syncStore = db.createObjectStore('syncQueue', { keyPath: 'id' });
            syncStore.createIndex('timestamp', 'timestamp');
            syncStore.createIndex('synced', 'synced');
        }
    }

    // Encryption setup
    setupEncryption() {
        // Generate or retrieve encryption key
        let key = localStorage.getItem('encryptionKey');
        if (!key) {
            key = this.generateEncryptionKey();
            localStorage.setItem('encryptionKey', key);
        }
        this.encryptionKey = key;
    }

    generateEncryptionKey() {
        const array = new Uint8Array(32);
        crypto.getRandomValues(array);
        return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
    }

    // Simple encryption/decryption (for demonstration)
    encrypt(data) {
        if (!this.encryptionKey) return data;
        
        try {
            const jsonString = JSON.stringify(data);
            const encoded = btoa(jsonString);
            return encoded;
        } catch (error) {
            console.error('Encryption failed:', error);
            return data;
        }
    }

    decrypt(encryptedData) {
        if (!this.encryptionKey || typeof encryptedData !== 'string') return encryptedData;
        
        try {
            const decoded = atob(encryptedData);
            return JSON.parse(decoded);
        } catch (error) {
            console.error('Decryption failed:', error);
            return encryptedData;
        }
    }

    // Database operations
    async saveAlert(alert) {
        if (!this.isInitialized) await this.init();
        
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['alerts'], 'readwrite');
            const store = transaction.objectStore('alerts');
            
            // Encrypt sensitive data
            const encryptedAlert = {
                ...alert,
                location: this.encrypt(alert.location),
                userId: this.encrypt(alert.userId)
            };
            
            const request = store.put(encryptedAlert);
            
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async getAlert(id) {
        if (!this.isInitialized) await this.init();
        
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['alerts'], 'readonly');
            const store = transaction.objectStore('alerts');
            const request = store.get(id);
            
            request.onsuccess = () => {
                const alert = request.result;
                if (alert) {
                    // Decrypt sensitive data
                    alert.location = this.decrypt(alert.location);
                    alert.userId = this.decrypt(alert.userId);
                }
                resolve(alert);
            };
            request.onerror = () => reject(request.error);
        });
    }

    async getAllAlerts(limit = 100) {
        if (!this.isInitialized) await this.init();
        
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['alerts'], 'readonly');
            const store = transaction.objectStore('alerts');
            const index = store.index('timestamp');
            const request = index.openCursor(null, 'prev');
            
            const alerts = [];
            let count = 0;
            
            request.onsuccess = (event) => {
                const cursor = event.target.result;
                if (cursor && count < limit) {
                    const alert = cursor.value;
                    // Decrypt sensitive data
                    alert.location = this.decrypt(alert.location);
                    alert.userId = this.decrypt(alert.userId);
                    alerts.push(alert);
                    count++;
                    cursor.continue();
                } else {
                    resolve(alerts);
                }
            };
            
            request.onerror = () => reject(request.error);
        });
    }

    async deleteAlert(id) {
        if (!this.isInitialized) await this.init();
        
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['alerts'], 'readwrite');
            const store = transaction.objectStore('alerts');
            const request = store.delete(id);
            
            request.onsuccess = () => resolve();
            request.onerror = () => reject(request.error);
        });
    }

    // User management
    async saveUser(user) {
        if (!this.isInitialized) await this.init();
        
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['users'], 'readwrite');
            const store = transaction.objectStore('users');
            
            // Encrypt sensitive data
            const encryptedUser = {
                ...user,
                email: this.encrypt(user.email),
                personalInfo: this.encrypt(user.personalInfo || {})
            };
            
            const request = store.put(encryptedUser);
            
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async getUser(id) {
        if (!this.isInitialized) await this.init();
        
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['users'], 'readonly');
            const store = transaction.objectStore('users');
            const request = store.get(id);
            
            request.onsuccess = () => {
                const user = request.result;
                if (user) {
                    // Decrypt sensitive data
                    user.email = this.decrypt(user.email);
                    user.personalInfo = this.decrypt(user.personalInfo);
                }
                resolve(user);
            };
            request.onerror = () => reject(request.error);
        });
    }

    // Location tracking
    async saveLocation(location) {
        if (!this.isInitialized) await this.init();
        
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['locations'], 'readwrite');
            const store = transaction.objectStore('locations');
            
            const locationRecord = {
                id: Date.now().toString(),
                ...location,
                coordinates: this.encrypt(location.coordinates),
                timestamp: new Date().toISOString()
            };
            
            const request = store.put(locationRecord);
            
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // Settings management
    async saveSetting(key, value) {
        if (!this.isInitialized) await this.init();
        
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['settings'], 'readwrite');
            const store = transaction.objectStore('settings');
            
            const setting = {
                key,
                value: this.encrypt(value),
                timestamp: new Date().toISOString()
            };
            
            const request = store.put(setting);
            
            request.onsuccess = () => resolve();
            request.onerror = () => reject(request.error);
        });
    }

    async getSetting(key) {
        if (!this.isInitialized) await this.init();
        
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['settings'], 'readonly');
            const store = transaction.objectStore('settings');
            const request = store.get(key);
            
            request.onsuccess = () => {
                const setting = request.result;
                if (setting) {
                    resolve(this.decrypt(setting.value));
                } else {
                    resolve(null);
                }
            };
            request.onerror = () => reject(request.error);
        });
    }

    // Logging
    async log(level, category, message, data = {}) {
        if (!this.isInitialized) await this.init();
        
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['logs'], 'readwrite');
            const store = transaction.objectStore('logs');
            
            const logEntry = {
                id: Date.now().toString() + '_' + Math.random().toString(36).substr(2, 9),
                level,
                category,
                message,
                data: this.encrypt(data),
                timestamp: new Date().toISOString(),
                userId: this.getCurrentUserId()
            };
            
            const request = store.put(logEntry);
            
            request.onsuccess = () => resolve();
            request.onerror = () => reject(request.error);
        });
    }

    // Sync queue management
    async addToSyncQueue(operation, data) {
        if (!this.isInitialized) await this.init();
        
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['syncQueue'], 'readwrite');
            const store = transaction.objectStore('syncQueue');
            
            const queueItem = {
                id: Date.now().toString() + '_' + Math.random().toString(36).substr(2, 9),
                operation,
                data: this.encrypt(data),
                timestamp: new Date().toISOString(),
                synced: false,
                attempts: 0
            };
            
            const request = store.put(queueItem);
            
            request.onsuccess = () => resolve(queueItem.id);
            request.onerror = () => reject(request.error);
        });
    }

    async getSyncQueue() {
        if (!this.isInitialized) await this.init();
        
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['syncQueue'], 'readonly');
            const store = transaction.objectStore('syncQueue');
            const index = store.index('synced');
            const request = index.getAll(false);
            
            request.onsuccess = () => {
                const items = request.result.map(item => ({
                    ...item,
                    data: this.decrypt(item.data)
                }));
                resolve(items);
            };
            request.onerror = () => reject(request.error);
        });
    }

    // Cleanup operations
    async cleanupOldData(daysToKeep = 30) {
        if (!this.isInitialized) await this.init();
        
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
        const cutoffTimestamp = cutoffDate.toISOString();
        
        const stores = ['alerts', 'locations', 'logs'];
        
        for (const storeName of stores) {
            await this.cleanupStore(storeName, cutoffTimestamp);
        }
    }

    async cleanupStore(storeName, cutoffTimestamp) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore('store');
            const index = store.index('timestamp');
            const range = IDBKeyRange.upperBound(cutoffTimestamp);
            const request = index.openCursor(range);
            
            request.onsuccess = (event) => {
                const cursor = event.target.result;
                if (cursor) {
                    cursor.delete();
                    cursor.continue();
                } else {
                    resolve();
                }
            };
            
            request.onerror = () => reject(request.error);
        });
    }

    // Export/Import
    async exportData() {
        const data = {
            alerts: await this.getAllAlerts(1000),
            timestamp: new Date().toISOString(),
            version: this.dbVersion
        };
        
        return data;
    }

    async importData(data) {
        if (!data.alerts) return;
        
        for (const alert of data.alerts) {
            await this.saveAlert(alert);
        }
    }

    // Utility methods
    getCurrentUserId() {
        return localStorage.getItem('userId') || 'anonymous';
    }

    async getStorageUsage() {
        if ('storage' in navigator && 'estimate' in navigator.storage) {
            return await navigator.storage.estimate();
        }
        return null;
    }

    // Database health check
    async healthCheck() {
        try {
            const testData = { test: 'health_check', timestamp: Date.now() };
            await this.saveSetting('health_check', testData);
            const retrieved = await this.getSetting('health_check');
            
            return {
                status: 'healthy',
                canWrite: true,
                canRead: retrieved !== null,
                encryption: this.encryptionKey !== null
            };
        } catch (error) {
            return {
                status: 'error',
                error: error.message,
                canWrite: false,
                canRead: false,
                encryption: false
            };
        }
    }
}

// Create global instance
window.databaseManager = new DatabaseManager();

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
    if (window.databaseManager) {
        // Perform health check
        const health = await window.databaseManager.healthCheck();
        console.log('Database health:', health);
        
        // Log application start
        await window.databaseManager.log('info', 'app', 'Application started', {
            userAgent: navigator.userAgent,
            timestamp: new Date().toISOString()
        });
    }
});
