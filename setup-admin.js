// Emergency Shield - Admin Setup
// إعداد حساب المسؤول الكامل

(function() {
    'use strict';

    // Admin configuration
    const ADMIN_CONFIG = {
        username: 'admin',
        password: '<PERSON>a<PERSON><PERSON><PERSON>@123',
        fullName: 'مسؤول النظام',
        email: '<EMAIL>',
        role: 'super_admin',
        permissions: [
            'view_all_alerts',
            'manage_users',
            'system_settings',
            'export_data',
            'send_messages',
            'suspend_users',
            'view_analytics',
            'manage_emergency_contacts',
            'system_maintenance'
        ],
        createdAt: new Date().toISOString(),
        lastLogin: null,
        isActive: true,
        mustChangePassword: false
    };

    // Setup admin account
    function setupAdminAccount() {
        try {
            // Create admin user in users storage
            const users = JSON.parse(localStorage.getItem('emergencyShieldUsers') || '{}');
            const adminUserId = 'admin_' + Date.now();
            
            users[adminUserId] = {
                userId: adminUserId,
                fullName: ADMIN_CONFIG.fullName,
                phoneNumber: '+970-admin-account',
                userRole: 'admin',
                locationArea: 'مركز التحكم',
                registrationDate: ADMIN_CONFIG.createdAt,
                lastLogin: ADMIN_CONFIG.lastLogin,
                lastActivity: ADMIN_CONFIG.createdAt,
                isVerified: true,
                isActive: ADMIN_CONFIG.isActive,
                ipAddress: '127.0.0.1',
                deviceInfo: 'Admin Console',
                permissions: ADMIN_CONFIG.permissions,
                email: ADMIN_CONFIG.email
            };
            
            localStorage.setItem('emergencyShieldUsers', JSON.stringify(users));
            
            // Create admin credentials
            const adminCredentials = {
                username: ADMIN_CONFIG.username,
                password: ADMIN_CONFIG.password, // In production, this should be hashed
                userId: adminUserId,
                role: ADMIN_CONFIG.role,
                permissions: ADMIN_CONFIG.permissions,
                createdAt: ADMIN_CONFIG.createdAt,
                lastLogin: ADMIN_CONFIG.lastLogin,
                isActive: ADMIN_CONFIG.isActive,
                mustChangePassword: ADMIN_CONFIG.mustChangePassword
            };
            
            localStorage.setItem('adminCredentials', JSON.stringify(adminCredentials));
            
            // Create admin session storage
            const adminSessions = JSON.parse(localStorage.getItem('adminSessions') || '{}');
            localStorage.setItem('adminSessions', JSON.stringify(adminSessions));
            
            console.log('✅ تم إنشاء حساب المسؤول بنجاح');
            console.log('👤 اسم المستخدم:', ADMIN_CONFIG.username);
            console.log('🔑 كلمة المرور:', ADMIN_CONFIG.password);
            
            return true;
        } catch (error) {
            console.error('❌ خطأ في إنشاء حساب المسؤول:', error);
            return false;
        }
    }

    // Verify admin login
    function verifyAdminLogin(username, password) {
        try {
            const adminCredentials = JSON.parse(localStorage.getItem('adminCredentials') || '{}');
            
            if (adminCredentials.username === username && 
                adminCredentials.password === password && 
                adminCredentials.isActive) {
                
                // Update last login
                adminCredentials.lastLogin = new Date().toISOString();
                localStorage.setItem('adminCredentials', JSON.stringify(adminCredentials));
                
                // Create session token
                const sessionToken = 'admin_session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                const sessionData = {
                    token: sessionToken,
                    userId: adminCredentials.userId,
                    username: adminCredentials.username,
                    role: adminCredentials.role,
                    permissions: adminCredentials.permissions,
                    createdAt: new Date().toISOString(),
                    expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours
                };
                
                localStorage.setItem('adminToken', sessionToken);
                localStorage.setItem('adminSession', JSON.stringify(sessionData));
                
                return {
                    success: true,
                    token: sessionToken,
                    user: {
                        username: adminCredentials.username,
                        role: adminCredentials.role,
                        permissions: adminCredentials.permissions
                    }
                };
            }
            
            return { success: false, message: 'بيانات الدخول غير صحيحة' };
        } catch (error) {
            console.error('خطأ في التحقق من بيانات المسؤول:', error);
            return { success: false, message: 'خطأ في النظام' };
        }
    }

    // Check if admin is logged in
    function isAdminLoggedIn() {
        try {
            const token = localStorage.getItem('adminToken');
            const sessionData = JSON.parse(localStorage.getItem('adminSession') || '{}');
            
            if (!token || !sessionData.token || token !== sessionData.token) {
                return false;
            }
            
            // Check if session is expired
            const expiresAt = new Date(sessionData.expiresAt);
            const now = new Date();
            
            if (now > expiresAt) {
                // Session expired, clean up
                localStorage.removeItem('adminToken');
                localStorage.removeItem('adminSession');
                return false;
            }
            
            return true;
        } catch (error) {
            console.error('خطأ في فحص جلسة المسؤول:', error);
            return false;
        }
    }

    // Get admin session data
    function getAdminSession() {
        try {
            if (!isAdminLoggedIn()) {
                return null;
            }
            
            return JSON.parse(localStorage.getItem('adminSession') || '{}');
        } catch (error) {
            console.error('خطأ في جلب بيانات جلسة المسؤول:', error);
            return null;
        }
    }

    // Logout admin
    function logoutAdmin() {
        try {
            localStorage.removeItem('adminToken');
            localStorage.removeItem('adminSession');
            console.log('تم تسجيل خروج المسؤول');
            return true;
        } catch (error) {
            console.error('خطأ في تسجيل خروج المسؤول:', error);
            return false;
        }
    }

    // Enhanced admin login for admin.js
    function enhanceAdminLogin() {
        // Override the login function in admin.js if it exists
        if (window.adminDashboard && typeof window.adminDashboard.login === 'function') {
            const originalLogin = window.adminDashboard.login.bind(window.adminDashboard);
            
            window.adminDashboard.login = function(username, password) {
                const result = verifyAdminLogin(username, password);
                if (result.success) {
                    // Call original login logic
                    return originalLogin(username, password);
                } else {
                    alert(result.message || 'فشل في تسجيل الدخول');
                    return false;
                }
            };
        }
    }

    // Create demo data for testing
    function createDemoData() {
        try {
            // Create some demo alerts
            const demoAlerts = [
                {
                    id: 'demo_alert_1',
                    type: 'ambulance',
                    priority: 'critical',
                    customMessage: 'حالة طبية طارئة - نوبة قلبية',
                    location: { latitude: 31.7683, longitude: 35.2137, accuracy: 10 },
                    timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
                    status: 'active',
                    senderInfo: {
                        fullName: 'أحمد محمد',
                        phoneNumber: '+970591234567',
                        userRole: 'citizen',
                        locationArea: 'القدس',
                        ipAddress: '*************',
                        deviceInfo: 'Android'
                    }
                },
                {
                    id: 'demo_alert_2',
                    type: 'fire',
                    priority: 'critical',
                    customMessage: 'حريق في المبنى - الطابق الثالث',
                    location: { latitude: 31.7700, longitude: 35.2150, accuracy: 15 },
                    timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
                    status: 'resolved',
                    senderInfo: {
                        fullName: 'فاطمة علي',
                        phoneNumber: '+970592345678',
                        userRole: 'citizen',
                        locationArea: 'القدس',
                        ipAddress: '*************',
                        deviceInfo: 'iOS'
                    }
                }
            ];
            
            localStorage.setItem('emergencyAlerts', JSON.stringify(demoAlerts));
            console.log('✅ تم إنشاء بيانات تجريبية');
        } catch (error) {
            console.error('خطأ في إنشاء البيانات التجريبية:', error);
        }
    }

    // Initialize admin setup
    function initializeAdminSetup() {
        // Setup admin account
        setupAdminAccount();
        
        // Create demo data
        createDemoData();
        
        // Enhance admin login when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', enhanceAdminLogin);
        } else {
            enhanceAdminLogin();
        }
        
        console.log('🛡️ تم تهيئة نظام المسؤول');
    }

    // Expose functions globally
    window.AdminSetup = {
        verifyAdminLogin,
        isAdminLoggedIn,
        getAdminSession,
        logoutAdmin,
        setupAdminAccount,
        createDemoData
    };

    // Auto-initialize
    initializeAdminSetup();

    console.log('🔐 نظام المسؤول جاهز');
    console.log('👤 اسم المستخدم: admin');
    console.log('🔑 كلمة المرور: JaMaL@123');
})();
